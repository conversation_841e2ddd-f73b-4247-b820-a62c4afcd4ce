#!/usr/bin/env python
"""
Test script to verify the 2-frame architecture is working correctly.
This script tests:
1. Model architecture with 2 input frames + masks
2. Dataset loading with correct frame/mask sequences
3. Forward pass compatibility
4. Loss calculation conformity with original
"""

import torch
import torch.nn as nn
import numpy as np
import argparse
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_args():
    """Create test arguments."""
    args = argparse.Namespace()
    args.rgb_max = 255.0
    args.sequence_length = 2  # 2 input frames
    args.batch_size = 2
    args.crop_size = [256, 320]
    args.stride = 64
    args.sample_rate = 1
    args.start_index = 0
    return args

def test_model_architecture():
    """Test the model architecture."""
    print("Testing SDCNet2DMaskStandalone architecture...")

    # Import directly to avoid CUDA dependencies
    import importlib.util
    spec = importlib.util.spec_from_file_location("sdc_net2d_mask_standalone",
                                                  "models/sdc_net2d_mask_standalone.py")
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    SDCNet2DMaskStandalone = module.SDCNet2DMaskStandalone

    args = create_test_args()
    model = SDCNet2DMaskStandalone(args)

    # Check input channels calculation
    expected_channels = 2 * 3 + 1 * 2 + 3  # 2 frames * 3 RGB + 1 flow * 2 + 3 masks = 11
    assert model.total_input_channels == expected_channels, f"Expected {expected_channels} channels, got {model.total_input_channels}"

    print(f"✓ Model created successfully")
    print(f"✓ Input channels: {model.total_input_channels} (expected: {expected_channels})")
    print(f"✓ Sequence length: {model.sequence_length}")

    return model

def test_forward_pass():
    """Test forward pass with synthetic data."""
    print("\nTesting forward pass...")

    model = test_model_architecture()
    model.eval()

    # Create synthetic input data
    batch_size = 2
    height, width = 256, 320

    # Create 3 frames: [t-1, t, t+1] where first 2 are input, last is target
    images = [
        torch.randn(batch_size, 3, height, width),  # t-1
        torch.randn(batch_size, 3, height, width),  # t
        torch.randn(batch_size, 3, height, width),  # t+1 (target)
    ]

    # Create 3 masks: [mask_t-1, mask_t, mask_t+1]
    masks = [
        torch.rand(batch_size, 1, height, width),   # mask_t-1
        torch.rand(batch_size, 1, height, width),   # mask_t
        torch.rand(batch_size, 1, height, width),   # mask_t+1 (target)
    ]

    input_dict = {
        'image': images,
        'mask': masks
    }

    # Forward pass
    with torch.no_grad():
        try:
            losses, pred_image, target_image, mask_pred, mask_target = model(input_dict)

            print(f"✓ Forward pass successful")
            print(f"✓ Prediction shape: {pred_image.shape}")
            print(f"✓ Target shape: {target_image.shape}")
            print(f"✓ Loss keys: {list(losses.keys())}")
            print(f"✓ Total loss: {losses['tot'].item():.6f}")
            print(f"✓ Color loss: {losses['color'].item():.6f}")
            print(f"✓ Gradient loss: {losses['color_gradient'].item():.6f}")
            print(f"✓ Smoothness loss: {losses['flow_smoothness'].item():.6f}")

            # Verify loss weights (should match original: 0.7, 0.2, 0.1)
            expected_total = 0.7 * losses['color'] + 0.2 * losses['color_gradient'] + 0.1 * losses['flow_smoothness']
            assert torch.allclose(losses['tot'], expected_total, atol=1e-6), "Loss weights don't match original"
            print(f"✓ Loss weights match original SDCNet2D")

            return True

        except Exception as e:
            print(f"✗ Forward pass failed: {e}")
            return False

def test_input_preparation():
    """Test input preparation logic."""
    print("\nTesting input preparation...")

    model = test_model_architecture()

    # Create test data
    batch_size = 1
    height, width = 128, 160

    images = [
        torch.randn(batch_size, 3, height, width),  # t-1
        torch.randn(batch_size, 3, height, width),  # t
        torch.randn(batch_size, 3, height, width),  # t+1
    ]

    masks = [
        torch.rand(batch_size, 1, height, width),   # mask_t-1
        torch.rand(batch_size, 1, height, width),   # mask_t
        torch.rand(batch_size, 1, height, width),   # mask_t+1
    ]

    input_dict = {
        'image': images,
        'mask': masks
    }

    # Test prepare_inputs
    input_images, input_masks, target_mask, last_image, target_image = model.prepare_inputs(input_dict)

    # Verify shapes and content
    assert len(input_images) == 2, f"Expected 2 input images, got {len(input_images)}"
    assert len(input_masks) == 2, f"Expected 2 input masks, got {len(input_masks)}"
    assert torch.equal(input_images[0], images[0]), "First input image mismatch"
    assert torch.equal(input_images[1], images[1]), "Second input image mismatch"
    assert torch.equal(target_image, images[2]), "Target image mismatch"
    assert torch.equal(last_image, images[1]), "Last image should be second input image"
    assert torch.equal(target_mask, masks[2]), "Target mask mismatch"

    print(f"✓ Input preparation correct")
    print(f"✓ Input images: {len(input_images)} frames")
    print(f"✓ Input masks: {len(input_masks)} masks")
    print(f"✓ Target image shape: {target_image.shape}")
    print(f"✓ Target mask shape: {target_mask.shape}")

def compare_with_original():
    """Compare architecture with original SDCNet2D."""
    print("\nComparing with original SDCNet2D...")

    try:
        # Import mask model directly
        import importlib.util
        spec = importlib.util.spec_from_file_location("sdc_net2d_mask_standalone",
                                                      "models/sdc_net2d_mask_standalone.py")
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        SDCNet2DMaskStandalone = module.SDCNet2DMaskStandalone

        args = create_test_args()

        # Create mask model
        mask_model = SDCNet2DMaskStandalone(args)

        # Compare key parameters (using known original values)
        original_sequence_length = 2  # Known from original SDCNet2D
        expected_original_channels = original_sequence_length * 3 + (original_sequence_length - 1) * 2  # 2*3 + 1*2 = 8

        print(f"✓ Original sequence_length: {original_sequence_length}")
        print(f"✓ Mask model sequence_length: {mask_model.sequence_length}")
        print(f"✓ Original input channels: {expected_original_channels}")
        print(f"✓ Mask model original channels: {mask_model.original_input_channels}")
        print(f"✓ Mask model total channels: {mask_model.total_input_channels}")

        # Verify they match
        assert mask_model.sequence_length == original_sequence_length, "Sequence length mismatch"
        assert mask_model.original_input_channels == expected_original_channels, "Original channels mismatch"

        print(f"✓ Architecture matches original SDCNet2D")

    except ImportError as e:
        print(f"⚠ Could not import original model for comparison: {e}")

def main():
    """Run all tests."""
    print("="*60)
    print("Testing 2-Frame SDCNet Architecture")
    print("="*60)

    try:
        # Test model architecture
        test_model_architecture()

        # Test input preparation
        test_input_preparation()

        # Test forward pass
        success = test_forward_pass()

        # Compare with original
        compare_with_original()

        print("\n" + "="*60)
        if success:
            print("✓ ALL TESTS PASSED!")
            print("✓ 2-frame architecture is working correctly")
            print("✓ Model is ready for training")
        else:
            print("✗ SOME TESTS FAILED!")
            return 1

    except Exception as e:
        print(f"\n✗ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return 1

    print("="*60)
    return 0

if __name__ == '__main__':
    exit(main())
