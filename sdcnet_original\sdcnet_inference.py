import os
import sys
import argparse
import cv2
import numpy as np
from PIL import Image

import torch
import torch.nn as nn
from torch.autograd import Variable

from models.sdc_net2d import *
import skimage.measure

from tqdm import tqdm
import pdb
import glob
import pickle
import shutil






parser = argparse.ArgumentParser()
parser.add_argument('--pretrained', default='../pretrained_models/sdc_cityscapes_vrec.pth.tar', type=str, metavar='PATH', help='path to trained video reconstruction checkpoint')
parser.add_argument('--flownet2_checkpoint', default='../pretrained_models/FlowNet2_checkpoint.pth.tar', type=str, metavar='PATH', help='path to flownet-2 best checkpoint')
parser.add_argument('--source_dir', default='~/research/flownet2-pytorch/frames/2011_09_26/2011_09_26_drive_0002_extract/image_03/data/', type=str, help='directory for data (default: Cityscapes root directory)')
parser.add_argument('--target_dir', default='Cityscapes/cs_aug/kitti/', type=str, help='directory to save augmented data')
parser.add_argument('--sequence_length', default=2, type=int, metavar="SEQUENCE_LENGTH",
                    help='number of interpolated frames (default : 2)')
parser.add_argument("--rgb_max", type=float, default = 255.)
parser.add_argument('--fp16', action='store_true', help='Run model in pseudo-fp16 mode (fp16 storage fp32 math).')
parser.add_argument('--vis', action='store_true', default=False, help='augment color encoded segmentation map')
parser.add_argument('--extrap_horizon', default=1, type=int, help='extrapolation horizon')
parser.add_argument('--num_ctx_frames', default=2, type=int, metavar="SEQUENCE_LENGTH",
                    help='number of interpolated frames (default : 2)')
parser.add_argument("--gpu", type=int, nargs="+", dest="gpu", required=True,
                  help="GPU device id")



def makedir(path):
    if not os.path.exists(path):
        os.makedirs(path)


def get_model():
    model = SDCNet2DRecon(args)
    checkpoint = torch.load(args.pretrained)
    args.start_epoch = 0 if 'epoch' not in checkpoint else checkpoint['epoch']
    state_dict = checkpoint if 'state_dict' not in checkpoint else checkpoint['state_dict']
    model.load_state_dict(state_dict, strict=False)
    print("Loaded checkpoint '{}' (at epoch {})".format(args.pretrained, args.start_epoch))
    return model

def get_data(img1_dir, img2_dir, img3_dir):
    img1_rgb = cv2.imread(img1_dir)
    img2_rgb = cv2.imread(img2_dir)
    img3_rgb = cv2.imread(img3_dir)

    h, w = img1_rgb.shape[0], img1_rgb.shape[1]
    th = (img1_rgb.shape[0]) // 64 * 64
    tw = (img1_rgb.shape[1]) // 64 * 64
    img1_rgb = img1_rgb[(h-th)//2:(h+th)//2, (w-tw)//2:(w+tw)//2,:]
    img2_rgb = img2_rgb[(h-th)//2:(h+th)//2, (w-tw)//2:(w+tw)//2,:]
    img3_rgb = img3_rgb[(h-th)//2:(h+th)//2, (w-tw)//2:(w+tw)//2,:]

    # pdb.set_trace()

    img1_rgb = img1_rgb.transpose((2,0,1))
    img2_rgb = img2_rgb.transpose((2,0,1))
    img3_rgb = img3_rgb.transpose((2,0,1))
    img1_rgb = np.expand_dims(img1_rgb, axis=0)
    img2_rgb = np.expand_dims(img2_rgb, axis=0)
    img3_rgb = np.expand_dims(img3_rgb, axis=0)
    img1_rgb = torch.from_numpy(img1_rgb.astype(np.float32))
    img2_rgb = torch.from_numpy(img2_rgb.astype(np.float32))
    img3_rgb = torch.from_numpy(img3_rgb.astype(np.float32))
    # gt2_rgb = torch.from_numpy(gt2_rgb.astype(np.float32))
    # gt2_labelid = torch.from_numpy(gt2_labelid.astype(np.float32))

    # return img1_rgb, img2_rgb, img3_rgb, gt2_rgb, gt2_labelid
    return img1_rgb, img2_rgb, img3_rgb

def remove_files_in_folder(folder):
    for f in os.listdir(folder):
        os.remove(os.path.join(folder, f))



def one_step_augmentation(model):

    propagate=1
    # Crea sottocartelle per ciascun valore di k
    target_dirs = []
    for k in range(propagate + 1):
        k_extension = f"k={k}"
        dir_to_create = os.path.join(args.target_dir, k_extension)
        target_dirs.append(dir_to_create)
        if not os.path.exists(dir_to_create):
            os.makedirs(dir_to_create)
    split_dir = os.path.expanduser(args.source_dir)
    #remove_files_in_folder(args.target_dir)  # Overwrite target dir with new pred images
    
    # Only retrieve the PNG files in the folder
    frames = sorted(glob.glob(os.path.join(split_dir, "*.png")))

    if not os.path.exists(args.target_dir):
        os.makedirs(args.target_dir)

    # Create black images for the first context frames
    for i in range(2):
        img = cv2.imread(frames[i])
        zeros_img = np.zeros_like(img)
        cv2.imwrite(os.path.join(args.target_dir, "{:010d}.png".format(i)), zeros_img)

    counter = 0

    for frame in tqdm(frames):
        seq_info = os.path.split(frame)[-1].split(".")[0]  # Extracting the file name without extension

        if counter == 0 or counter == (len(frames) - 1):
            counter += 1
            continue

        counter += 1

        try:
            seq_id2 = seq_info
            seq_id1 = "{:010d}".format(int(seq_id2) - 1)
            seq_id3 = "{:010d}".format(int(seq_id2) + 1)
        except ValueError:
            continue

        source_im1 = os.path.join(split_dir, seq_id1 + ".png")
        source_im2 = os.path.join(split_dir, seq_id2 + ".png")
        source_im3 = os.path.join(split_dir, seq_id3 + ".png")

        if not all(map(os.path.exists, [source_im1, source_im2, source_im3])):
            continue

        img1_rgb, img2_rgb, img3_rgb = get_data(source_im1, source_im2, source_im3)

        img1_rgb = Variable(img1_rgb).contiguous().cuda()
        img2_rgb = Variable(img2_rgb).contiguous().cuda()
        img3_rgb = Variable(img3_rgb).contiguous().cuda()

        input_dict = {'image': [img1_rgb, img2_rgb, img3_rgb]}

        _, pred3_rgb, _ = model(input_dict)
        pred3_rgb_img = (pred3_rgb.data.cpu().numpy().squeeze().transpose(1, 2, 0)).astype(np.uint8)

        target_im3 = os.path.join(target_dirs[k], f"{seq_id3}.png")
        cv2.imwrite(target_im3, pred3_rgb_img)
    k = 1
    step = 1
    num_ctx_frames = int(args.num_ctx_frames)
    for idx in range(num_ctx_frames, num_ctx_frames + propagate - 1):
        img_cpy = os.path.join(target_dirs[k], f"{idx:010d}.png")
        while k < propagate:
            k += 1
            target_img = os.path.join(target_dirs[k], f"{idx:010d}.png")
            shutil.copy(img_cpy, target_img)

        step += 1
        k = step







def new_one_step_augmentation(model, source_dir, target_dir, h=1, step=1):
    if h < 1:
        raise ValueError("h deve essere maggiore o uguale a 1")

    # Definisci il target_dir specifico per h
    current_target_dir = os.path.join(target_dir, f"k={h}")

    # Crea la directory target per l'orizzonte corrente se non esiste
    if not os.path.exists(current_target_dir):
        os.makedirs(current_target_dir)

    # Caso base: h=1
    if h == 1:
        # Leggi i frame dalla source_dir
        frames = sorted(glob.glob(os.path.join(source_dir, "*.png")))

        # Crea n black frames nella current_target_dir
        for i in range(2):
            img = cv2.imread(frames[i])
            zeros_img = np.zeros_like(img)
            cv2.imwrite(os.path.join(current_target_dir, "{:010d}.png".format(i)), zeros_img)

        # Processa i frame
        counter = 0
        for frame in tqdm(frames):
            seq_info = os.path.split(frame)[-1].split(".")[0]  # Estrai il nome del file senza estensione

            if counter == 0 or counter == (len(frames) - 1):
                counter += 1
                continue

            counter += 1

            try:
                seq_id2 = seq_info
                seq_id1 = "{:010d}".format(int(seq_id2) - 1)
                seq_id3 = "{:010d}".format(int(seq_id2) + 1)
            except ValueError:
                continue

            source_im1 = os.path.join(source_dir, seq_id1 + ".png")
            source_im2 = os.path.join(source_dir, seq_id2 + ".png")
            source_im3 = os.path.join(source_dir, seq_id3 + ".png")

            if not all(map(os.path.exists, [source_im1, source_im2, source_im3])):
                continue

            img1_rgb, img2_rgb, img3_rgb = get_data(source_im1, source_im2, source_im3)

            img1_rgb = Variable(img1_rgb).contiguous().cuda()
            img2_rgb = Variable(img2_rgb).contiguous().cuda()
            img3_rgb = Variable(img3_rgb).contiguous().cuda()

            input_dict = {'image': [img1_rgb, img2_rgb, img3_rgb]}

            _, pred3_rgb, _ = model(input_dict)
            pred3_rgb_img = (pred3_rgb.data.cpu().numpy().squeeze().transpose(1, 2, 0)).astype(np.uint8)

            target_im3 = os.path.join(current_target_dir, f"{seq_id3}.png")

            gt_img_shape = cv2.imread(source_im2).shape
            pred3_rgb_img = cv2.resize(pred3_rgb_img, (gt_img_shape[1], gt_img_shape[0]))
            cv2.imwrite(target_im3, pred3_rgb_img)

    # Passo ricorsivo: h > 1
    else:
        # Definisci il nuovo source_dir e target_dir per h-1
        new_source_dir = os.path.join(target_dir, f"k={h-1}")

        # Prima chiamata ricorsiva per h-1
        new_one_step_augmentation(model, source_dir, target_dir, h=h-1, step=step)

        # Poi estrapoliamo i frame per h usando il nuovo source_dir (ottenuto da h-1)
        new_one_step_augmentation(model, new_source_dir, target_dir, h=1, step=step)








def resize_images(images, target_size):
    resized_images = []
    for image in images:
        resized_image = cv2.resize(image, (target_size[1], target_size[0]))  # cv2.resize usa (width, height)
        resized_images.append(resized_image)
    return resized_images

def normalize_and_resize_images(img1, img2, img3):
    # Converti le immagini da numpy array a torch tensor
    img1 = torch.from_numpy(img1).permute(2, 0, 1).float().unsqueeze(0)
    img2 = torch.from_numpy(img2).permute(2, 0, 1).float().unsqueeze(0)
    img3 = torch.from_numpy(img3).permute(2, 0, 1).float().unsqueeze(0)

    # Verifica le dimensioni minime
    h_min = min(img1.size(2), img2.size(2), img3.size(2))
    w_min = min(img1.size(3), img2.size(3), img3.size(3))

    # Ridimensiona le immagini per adattarsi alle dimensioni minime
    img1 = torch.nn.functional.interpolate(img1, size=(h_min, w_min), mode='bilinear', align_corners=False)
    img2 = torch.nn.functional.interpolate(img2, size=(h_min, w_min), mode='bilinear', align_corners=False)
    img3 = torch.nn.functional.interpolate(img3, size=(h_min, w_min), mode='bilinear', align_corners=False)

    return img1, img2, img3





#



if __name__ == "__main__":
    global args
    args = parser.parse_args()

    # set gpu usage
    os.environ["CUDA_DEVICE_ORDER"]="PCI_BUS_ID"
    os.environ["CUDA_VISIBLE_DEVICES"]=str(args.gpu[0])

    # Load pre-trained video reconstruction model (uncomment, this was commented to test other functions quickly)
    net = get_model()
    net.eval()
    net = net.cuda()


    # Config paths
    if not os.path.exists(args.target_dir):
        os.makedirs(args.target_dir)
    new_one_step_augmentation(net,args.source_dir,args.target_dir,h=args.extrap_horizon, step=1)

    """if int(args.extrap_horizon) == 1:
        one_step_augmentation(net)
    else:
        multi_step_augmentation(net, propagate=int(args.extrap_horizon))"""
