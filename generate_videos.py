#!/usr/bin/env python
"""
Video Generation Script for SDCNet2D Results

This script takes the output from inference_test.py and generates MP4 videos
using FFmpeg for easy comparison between predicted and ground truth frames.

Usage:
    python generate_videos.py --input_dir predicted_2024_01_15_14_30_00
"""

import os
import sys
import argparse
import subprocess
import glob
from datetime import datetime
from tqdm import tqdm

def create_args():
    """Create argument parser for video generation."""
    parser = argparse.ArgumentParser(description='Generate Videos from SDCNet2D Predictions')

    # Input/Output paths
    parser.add_argument('--input_dir', required=True, help='Input directory from inference_test.py')
    parser.add_argument('--output_dir', default=None, help='Output directory for videos (auto-generated if not provided)')

    # Video parameters
    parser.add_argument('--fps', type=int, default=10, help='Frames per second for output videos')
    parser.add_argument('--quality', type=str, default='high', choices=['low', 'medium', 'high'],
                       help='Video quality preset')
    parser.add_argument('--format', type=str, default='mp4', choices=['mp4', 'avi', 'mov'],
                       help='Output video format')

    # Comparison options
    parser.add_argument('--create_comparison', action='store_true',
                       help='Create side-by-side comparison videos')
    parser.add_argument('--create_individual', action='store_true', default=True,
                       help='Create individual videos for predicted and ground truth')

    # FFmpeg options
    parser.add_argument('--ffmpeg_path', default='ffmpeg', help='Path to FFmpeg executable')
    parser.add_argument('--overwrite', action='store_true', help='Overwrite existing videos')

    return parser.parse_args()

def check_ffmpeg(ffmpeg_path):
    """Check if FFmpeg is available."""
    try:
        result = subprocess.run([ffmpeg_path, '-version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ FFmpeg found: {ffmpeg_path}")
            return True
        else:
            print(f"❌ FFmpeg not working: {ffmpeg_path}")
            return False
    except Exception as e:
        print(f"❌ FFmpeg not found: {e}")
        return False

def get_quality_settings(quality):
    """Get FFmpeg quality settings."""
    settings = {
        'low': ['-crf', '28', '-preset', 'fast'],
        'medium': ['-crf', '23', '-preset', 'medium'],
        'high': ['-crf', '18', '-preset', 'slow']
    }
    return settings.get(quality, settings['medium'])

def natural_sort_key(filename):
    """Natural sorting key for filenames with numbers."""
    import re
    return [int(text) if text.isdigit() else text.lower() for text in re.split('([0-9]+)', filename)]

def create_video_from_frames(frame_dir, output_path, fps, quality_settings, ffmpeg_path, overwrite=False):
    """Create video from frame directory using FFmpeg."""
    if not os.path.exists(frame_dir):
        print(f"❌ Frame directory not found: {frame_dir}")
        return False

    # Get frame files
    frame_files = glob.glob(os.path.join(frame_dir, "*.jpg")) + \
                  glob.glob(os.path.join(frame_dir, "*.png"))

    if not frame_files:
        print(f"❌ No frame files found in: {frame_dir}")
        return False

    # Sort frames naturally
    frame_files.sort(key=lambda x: natural_sort_key(os.path.basename(x)))

    print(f"  Found {len(frame_files)} frames")
    print(f"  First frame: {os.path.basename(frame_files[0]) if frame_files else 'None'}")
    print(f"  Last frame: {os.path.basename(frame_files[-1]) if frame_files else 'None'}")
    print(f"  Expected duration: {len(frame_files)/fps:.1f} seconds at {fps} FPS")

    # Check if output exists
    if os.path.exists(output_path) and not overwrite:
        print(f"  ⚠️  Video already exists: {output_path} (use --overwrite to replace)")
        return True

    # Create temporary file list for FFmpeg
    temp_list_path = output_path + "_temp_list.txt"
    try:
        with open(temp_list_path, 'w') as f:
            for frame_file in frame_files:
                f.write(f"file '{os.path.abspath(frame_file)}'\n")

        # FFmpeg command using image sequence instead of concat
        # Get the pattern for the first frame to determine naming
        first_frame = frame_files[0]
        frame_dir_abs = os.path.abspath(frame_dir)

        # Try to detect frame naming pattern
        import re
        frame_name = os.path.basename(first_frame)

        # Check if frames are numbered sequentially
        if re.search(r'\d+', frame_name):
            # Use image sequence input (more reliable)
            frame_pattern = re.sub(r'\d+', '%d', frame_name)
            frame_pattern_path = os.path.join(frame_dir_abs, frame_pattern)

            cmd = [
                ffmpeg_path,
                '-start_number', '1',
                '-i', frame_pattern_path,
                '-vframes', str(len(frame_files)),
                '-r', str(fps),
                '-pix_fmt', 'yuv420p'
            ] + quality_settings + [
                '-y' if overwrite else '-n',
                output_path
            ]
        else:
            # Fallback to concat method
            cmd = [
                ffmpeg_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', temp_list_path,
                '-r', str(fps),
                '-pix_fmt', 'yuv420p'
            ] + quality_settings + [
                '-y' if overwrite else '-n',
                output_path
            ]

        # Run FFmpeg
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"  ✓ Video created: {output_path}")
            return True
        else:
            print(f"  ❌ FFmpeg error: {result.stderr}")
            return False

    except Exception as e:
        print(f"  ❌ Error creating video: {e}")
        return False
    finally:
        # Clean up temporary file
        if os.path.exists(temp_list_path):
            os.remove(temp_list_path)

def create_comparison_video(pred_dir, gt_dir, output_path, fps, quality_settings, ffmpeg_path, overwrite=False):
    """Create side-by-side comparison video."""
    if not os.path.exists(pred_dir) or not os.path.exists(gt_dir):
        print(f"❌ Missing directories for comparison")
        return False

    # Get frame files
    pred_files = sorted(glob.glob(os.path.join(pred_dir, "*.jpg")) +
                       glob.glob(os.path.join(pred_dir, "*.png")),
                       key=lambda x: natural_sort_key(os.path.basename(x)))

    gt_files = sorted(glob.glob(os.path.join(gt_dir, "*.jpg")) +
                     glob.glob(os.path.join(gt_dir, "*.png")),
                     key=lambda x: natural_sort_key(os.path.basename(x)))

    if len(pred_files) != len(gt_files):
        print(f"❌ Mismatch in frame count: pred={len(pred_files)}, gt={len(gt_files)}")
        return False

    if not pred_files:
        print(f"❌ No frames found for comparison")
        return False

    print(f"  Creating comparison with {len(pred_files)} frames")

    # Check if output exists
    if os.path.exists(output_path) and not overwrite:
        print(f"  ⚠️  Comparison video already exists: {output_path}")
        return True

    # Create temporary file lists
    pred_list_path = output_path + "_pred_list.txt"
    gt_list_path = output_path + "_gt_list.txt"

    try:
        with open(pred_list_path, 'w') as f:
            for frame_file in pred_files:
                f.write(f"file '{os.path.abspath(frame_file)}'\n")

        with open(gt_list_path, 'w') as f:
            for frame_file in gt_files:
                f.write(f"file '{os.path.abspath(frame_file)}'\n")

        # FFmpeg command for side-by-side comparison
        cmd = [
            ffmpeg_path,
            '-f', 'concat', '-safe', '0', '-i', pred_list_path,
            '-f', 'concat', '-safe', '0', '-i', gt_list_path,
            '-filter_complex', '[0:v][1:v]hstack=inputs=2[v]',
            '-map', '[v]',
            '-r', str(fps),
            '-pix_fmt', 'yuv420p'
        ] + quality_settings + [
            '-y' if overwrite else '-n',
            output_path
        ]

        # Run FFmpeg
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"  ✓ Comparison video created: {output_path}")
            return True
        else:
            print(f"  ❌ FFmpeg error: {result.stderr}")
            return False

    except Exception as e:
        print(f"  ❌ Error creating comparison video: {e}")
        return False
    finally:
        # Clean up temporary files
        for temp_file in [pred_list_path, gt_list_path]:
            if os.path.exists(temp_file):
                os.remove(temp_file)

def generate_videos(args):
    """Generate videos from prediction frames."""
    # Check input directory
    if not os.path.exists(args.input_dir):
        print(f"❌ Input directory not found: {args.input_dir}")
        return False

    # Create output directory
    if args.output_dir is None:
        timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        args.output_dir = f"videos_{timestamp}"

    os.makedirs(args.output_dir, exist_ok=True)
    print(f"Output directory: {args.output_dir}")

    # Get quality settings
    quality_settings = get_quality_settings(args.quality)

    # Find video directories
    pred_base_dir = os.path.join(args.input_dir, "predicted")
    gt_base_dir = os.path.join(args.input_dir, "ground_truth")

    if not os.path.exists(pred_base_dir):
        print(f"❌ Predicted frames directory not found: {pred_base_dir}")
        return False

    if not os.path.exists(gt_base_dir):
        print(f"❌ Ground truth frames directory not found: {gt_base_dir}")
        return False

    # Get video names
    video_names = [d for d in os.listdir(pred_base_dir)
                   if os.path.isdir(os.path.join(pred_base_dir, d))]

    if not video_names:
        print(f"❌ No video directories found in: {pred_base_dir}")
        return False

    print(f"Found {len(video_names)} videos to process")

    # Process each video
    success_count = 0

    for video_name in tqdm(video_names, desc="Generating videos"):
        print(f"\nProcessing video: {video_name}")

        pred_video_dir = os.path.join(pred_base_dir, video_name)
        gt_video_dir = os.path.join(gt_base_dir, video_name)

        video_success = True

        # Create individual videos
        if args.create_individual:
            # Predicted video
            pred_output = os.path.join(args.output_dir, f"{video_name}_predicted.{args.format}")
            if not create_video_from_frames(pred_video_dir, pred_output, args.fps,
                                          quality_settings, args.ffmpeg_path, args.overwrite):
                video_success = False

            # Ground truth video
            gt_output = os.path.join(args.output_dir, f"{video_name}_ground_truth.{args.format}")
            if not create_video_from_frames(gt_video_dir, gt_output, args.fps,
                                          quality_settings, args.ffmpeg_path, args.overwrite):
                video_success = False

        # Create comparison video
        if args.create_comparison:
            comp_output = os.path.join(args.output_dir, f"{video_name}_comparison.{args.format}")
            if not create_comparison_video(pred_video_dir, gt_video_dir, comp_output, args.fps,
                                         quality_settings, args.ffmpeg_path, args.overwrite):
                video_success = False

        if video_success:
            success_count += 1

    # Summary
    print(f"\n🎉 Video generation completed!")
    print(f"Successfully processed: {success_count}/{len(video_names)} videos")
    print(f"Output directory: {args.output_dir}")

    return success_count == len(video_names)

def main():
    """Main function."""
    args = create_args()

    print("=" * 60)
    print("SDCNet2D Video Generation")
    print("=" * 60)
    print(f"Input directory: {args.input_dir}")
    print(f"FPS: {args.fps}")
    print(f"Quality: {args.quality}")
    print(f"Format: {args.format}")
    print(f"Individual videos: {args.create_individual}")
    print(f"Comparison videos: {args.create_comparison}")
    print("=" * 60)

    # Check FFmpeg
    if not check_ffmpeg(args.ffmpeg_path):
        print("❌ FFmpeg is required but not found. Please install FFmpeg.")
        return False

    try:
        success = generate_videos(args)
        if success:
            print(f"\n✅ All videos generated successfully!")
        else:
            print(f"\n⚠️  Some videos failed to generate. Check the logs above.")
        return success

    except Exception as e:
        print(f"❌ Video generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
