#!/usr/bin/env python3
"""
Quick test to verify the offset fix works.
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import os
import sys

# Add models directory to path
sys.path.append('models')

def test_warping_simple():
    """Simple test of the warping function."""
    print("Testing warping fix...")
    
    try:
        from sdc_net2d_mask_original import NativeResample2d
        
        # Create a simple test image
        img = torch.zeros(1, 3, 64, 64)
        img[0, 0, 20:40, 20:40] = 1.0  # Red square
        
        # Test zero flow (should be identity)
        zero_flow = torch.zeros(1, 2, 64, 64)
        warper = NativeResample2d(bilinear=True)
        warped_zero = warper(img, zero_flow)
        
        diff_zero = torch.abs(warped_zero - img).max().item()
        print(f"Zero flow difference: {diff_zero:.6f}")
        
        # Test simple translation
        flow = torch.zeros(1, 2, 64, 64)
        flow[0, 0, :, :] = 5  # Move 5 pixels right
        warped_translate = warper(img, flow)
        
        # Check if the square moved correctly
        original_center = torch.where(img[0, 0] > 0.5)
        warped_center = torch.where(warped_translate[0, 0] > 0.5)
        
        if len(original_center[0]) > 0 and len(warped_center[0]) > 0:
            orig_y = original_center[0].float().mean()
            orig_x = original_center[1].float().mean()
            warp_y = warped_center[0].float().mean()
            warp_x = warped_center[1].float().mean()
            
            shift_x = warp_x - orig_x
            shift_y = warp_y - orig_y
            
            print(f"Expected shift: (5, 0)")
            print(f"Actual shift: ({shift_x:.1f}, {shift_y:.1f})")
            
            if abs(shift_x - 5) < 1 and abs(shift_y) < 1:
                print("✓ Warping appears to be FIXED!")
                return True
            else:
                print("✗ Warping still has issues")
                return False
        else:
            print("? Could not detect pattern")
            return None
            
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_with_trained_model():
    """Test with a trained model."""
    print("\nTesting with trained model...")
    
    try:
        from sdc_net2d_mask_original import SDCNet2DMaskOriginal
        import argparse
        
        # Create args
        args = argparse.Namespace()
        args.rgb_max = 255.0
        args.sequence_length = 2
        
        # Create model
        model = SDCNet2DMaskOriginal(args)
        
        # Try to load a checkpoint
        checkpoint_path = 'training_output/sdcmask_train/model_best.pth.tar'
        if os.path.exists(checkpoint_path):
            print(f"Loading checkpoint: {checkpoint_path}")
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            model.load_state_dict(checkpoint['state_dict'])
            model.eval()
            
            # Create synthetic test data
            images = []
            masks = []
            for i in range(3):
                img = torch.randn(1, 3, 256, 320) * 50 + 128
                mask = torch.ones(1, 1, 256, 320) * 255
                images.append(img)
                masks.append(mask)
            
            input_dict = {'image': images, 'mask': masks}
            
            # Run inference
            with torch.no_grad():
                losses, pred_image, target_image = model(input_dict)
                
            print(f"Inference successful! Color loss: {losses['color']:.6f}")
            
            # Save a quick comparison
            os.makedirs('quick_test_results', exist_ok=True)
            
            pred_np = pred_image[0].permute(1, 2, 0).clamp(0, 255).numpy().astype(np.uint8)
            target_np = target_image[0].permute(1, 2, 0).clamp(0, 255).numpy().astype(np.uint8)
            
            Image.fromarray(pred_np).save('quick_test_results/predicted.png')
            Image.fromarray(target_np).save('quick_test_results/target.png')
            
            print("Results saved to quick_test_results/")
            return True
        else:
            print("No trained model found")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run tests."""
    print("Quick Offset Fix Test")
    print("=" * 30)
    
    # Test basic warping
    warp_result = test_warping_simple()
    
    # Test with trained model
    model_result = test_with_trained_model()
    
    print("\n" + "=" * 30)
    if warp_result is True:
        print("✓ Basic warping test PASSED")
    else:
        print("✗ Basic warping test FAILED")
    
    if model_result is True:
        print("✓ Model test PASSED")
    else:
        print("✗ Model test FAILED")

if __name__ == "__main__":
    main()
