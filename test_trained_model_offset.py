#!/usr/bin/env python3
"""
Test the trained model with the offset fix to see if the problem is resolved.
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import os
import sys
import argparse
from datetime import datetime

# Add models directory to path
sys.path.append('models')

def create_args():
    """Create arguments for model initialization."""
    args = argparse.Namespace()
    args.rgb_max = 255.0
    args.sequence_length = 2
    return args

def load_trained_model():
    """Load the best trained model."""
    from sdc_net2d_mask_original import SDCNet2DMaskOriginal

    args = create_args()
    model = SDCNet2DMaskOriginal(args)

    # Try to load the best checkpoint
    checkpoint_paths = [
        'training_output/sdcmask_train/model_best.pth.tar',
        'training_output/sdcmask_train/checkpoint_epoch_209.pth.tar',
        'training_output/sdcmask_train/checkpoint_epoch_204.pth.tar',
        'training_output/sdcmask_train/checkpoint_epoch_203.pth.tar'
    ]

    checkpoint = None
    for path in checkpoint_paths:
        if os.path.exists(path):
            print(f"Loading checkpoint: {path}")
            checkpoint = torch.load(path, map_location='cpu')
            break

    if checkpoint is None:
        print("No checkpoint found!")
        return None

    # Load model state
    if 'state_dict' in checkpoint:
        model.load_state_dict(checkpoint['state_dict'])
    elif 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)

    model.eval()
    print(f"Model loaded successfully!")

    if 'epoch' in checkpoint:
        print(f"Epoch: {checkpoint['epoch']}")
    if 'best_loss' in checkpoint:
        print(f"Best loss: {checkpoint['best_loss']:.6f}")

    return model

def load_real_data():
    """Load real data from the dataset."""
    # Look for real dataset images
    base_paths = [
        'dataset/train/blackswan',
        'dataset/train/bmx-trees',
        'dataset/train/breakdance',
        'dataset/train/camel',
        'dataset/train/car-roundabout'
    ]

    for base_path in base_paths:
        if os.path.exists(base_path):
            print(f"Using data from: {base_path}")

            # Load 3 consecutive frames
            images = []
            masks = []

            for i in range(3):
                img_path = os.path.join(base_path, f"{i:05d}.jpg")
                mask_path = os.path.join(base_path, f"{i:05d}.png")

                if os.path.exists(img_path) and os.path.exists(mask_path):
                    # Load image
                    img = Image.open(img_path).convert('RGB')
                    img_array = np.array(img)
                    images.append(img_array)

                    # Load mask
                    mask = Image.open(mask_path).convert('L')
                    mask_array = np.array(mask)
                    masks.append(mask_array)
                else:
                    print(f"Missing files: {img_path} or {mask_path}")
                    break

            if len(images) == 3 and len(masks) == 3:
                return images, masks, base_path

    print("No suitable real data found!")
    return None, None, None

def test_model_with_real_data():
    """Test the model with real data and check for offset."""
    print("Testing trained model with real data...")

    # Load model
    model = load_trained_model()
    if model is None:
        return False

    # Load real data
    images, masks, data_path = load_real_data()
    if images is None:
        return False

    print(f"Loaded {len(images)} images from {data_path}")

    # Convert to tensors
    image_tensors = []
    mask_tensors = []

    for img, mask in zip(images, masks):
        # Convert image to tensor [3, H, W]
        img_tensor = torch.from_numpy(img).permute(2, 0, 1).float()
        image_tensors.append(img_tensor)

        # Convert mask to tensor [1, H, W]
        mask_tensor = torch.from_numpy(mask).unsqueeze(0).float()
        mask_tensors.append(mask_tensor)

    # Create batch [1, 3, H, W] for each
    batch_images = [img.unsqueeze(0) for img in image_tensors]
    batch_masks = [mask.unsqueeze(0) for mask in mask_tensors]

    # Prepare input dict
    input_dict = {
        'image': batch_images,
        'mask': batch_masks
    }

    # Run inference
    print("Running inference...")
    with torch.no_grad():
        try:
            losses, pred_image, target_image = model(input_dict)

            print(f"Inference successful!")
            print(f"Color loss: {losses['color']:.6f}")
            print(f"Total loss: {losses['tot']:.6f}")

            # Save results for visual inspection
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = f'offset_test_results_{timestamp}'
            os.makedirs(output_dir, exist_ok=True)

            # Convert tensors to images
            pred_np = pred_image[0].permute(1, 2, 0).clamp(0, 255).numpy().astype(np.uint8)
            target_np = target_image[0].permute(1, 2, 0).clamp(0, 255).numpy().astype(np.uint8)
            input_np = batch_images[1][0].permute(1, 2, 0).clamp(0, 255).numpy().astype(np.uint8)

            # Save individual images
            Image.fromarray(input_np).save(os.path.join(output_dir, 'input_frame.png'))
            Image.fromarray(target_np).save(os.path.join(output_dir, 'target_frame.png'))
            Image.fromarray(pred_np).save(os.path.join(output_dir, 'predicted_frame.png'))

            # Create comparison image
            comparison = np.concatenate([input_np, target_np, pred_np], axis=1)
            Image.fromarray(comparison).save(os.path.join(output_dir, 'comparison.png'))

            # Create difference image
            diff = np.abs(target_np.astype(np.float32) - pred_np.astype(np.float32))
            diff_normalized = (diff / diff.max() * 255).astype(np.uint8)
            Image.fromarray(diff_normalized).save(os.path.join(output_dir, 'difference.png'))

            print(f"\nResults saved to {output_dir}/")
            print("- input_frame.png: Input frame (t)")
            print("- target_frame.png: Target frame (t+1)")
            print("- predicted_frame.png: Predicted frame")
            print("- comparison.png: Side-by-side comparison")
            print("- difference.png: Absolute difference")

            # Analyze offset by computing center of mass of foreground
            def compute_center_of_mass(img, mask):
                """Compute center of mass of foreground region."""
                if mask.max() == 0:
                    return None, None

                # Use mask to identify foreground
                foreground = mask > 128
                if not np.any(foreground):
                    return None, None

                coords = np.where(foreground)
                center_y = np.mean(coords[0])
                center_x = np.mean(coords[1])
                return center_y, center_x

            # Get masks
            target_mask_np = batch_masks[2][0, 0].numpy()

            # Compute centers of mass
            target_center = compute_center_of_mass(target_np, target_mask_np)

            # For predicted image, we need to estimate the foreground
            # Use color similarity to the target foreground
            pred_gray = np.mean(pred_np, axis=2)
            target_gray = np.mean(target_np, axis=2)

            # Simple foreground estimation based on difference from background
            pred_fg_estimate = np.abs(pred_gray - np.mean(pred_gray)) > 20
            pred_center = compute_center_of_mass(pred_np, pred_fg_estimate.astype(np.uint8) * 255)

            if target_center[0] is not None and pred_center[0] is not None:
                offset_y = pred_center[0] - target_center[0]
                offset_x = pred_center[1] - target_center[1]

                print(f"\nForeground center analysis:")
                print(f"  Target center: ({target_center[1]:.1f}, {target_center[0]:.1f})")
                print(f"  Predicted center: ({pred_center[1]:.1f}, {pred_center[0]:.1f})")
                print(f"  Offset: ({offset_x:.1f}, {offset_y:.1f}) pixels")

                total_offset = np.sqrt(offset_x**2 + offset_y**2)
                print(f"  Total offset magnitude: {total_offset:.1f} pixels")

                if total_offset < 10:
                    print("✓ Offset appears to be FIXED (< 10 pixels)")
                    return True
                elif total_offset < 20:
                    print("? Offset is REDUCED but still present (10-20 pixels)")
                    return None
                else:
                    print("✗ Significant offset still present (> 20 pixels)")
                    return False
            else:
                print("? Could not compute centers of mass for offset analysis")

                # Alternative: simple visual inspection message
                print("\nVisual inspection required:")
                print("  Please check the generated comparison images to assess offset.")
                return None

        except Exception as e:
            print(f"Error during inference: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Run the trained model offset test."""
    print("Testing trained SDCNet model for offset fix")
    print("=" * 50)

    result = test_model_with_real_data()

    print("\n" + "=" * 50)
    if result is True:
        print("✓ Test PASSED: Offset appears to be FIXED!")
    elif result is False:
        print("✗ Test FAILED: Offset still present")
    else:
        print("? Test INCONCLUSIVE: Offset reduced but not completely fixed")

    print("\nCheck the generated images in the output directory for visual confirmation.")

if __name__ == "__main__":
    main()
