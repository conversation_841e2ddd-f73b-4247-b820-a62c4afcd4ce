# SDCNet2D Dataset Structure

## 📁 Directory Overview

This directory contains the complete dataset structure for SDCNet2D with binary mask support.

```
dataset/
├── train/          # Training data (70-80% of total)
│   ├── X/          # Input: frames + masks
│   └── Y/          # Target: frames only
├── val/            # Validation data (10-15% of total)
│   ├── X/          # Input: frames + masks
│   └── Y/          # Target: frames only
├── test/           # Test data (10-15% of total)
│   ├── X/          # Input: frames + masks
│   └── Y/          # Target: frames only
├── EXAMPLE_STRUCTURE.md     # Complete example with file names
└── README.md               # This file
```

## 🚀 Quick Start

1. **Read the guides**:
   - `../DATASET_STRUCTURE_GUIDE.md` - Complete documentation
   - `EXAMPLE_STRUCTURE.md` - Concrete example
   - `../dataset_preparation_scripts/README.md` - Helper scripts

2. **Prepare your data**:
   - Extract frames from videos
   - Generate binary masks (0=background, 255=object)
   - Organize according to the documented structure

3. **Validate your dataset**:
   ```bash
   python ../dataset_preparation_scripts/validate_dataset.py --dataset_root ./
   ```

4. **Start training**:
   ```bash
   python ../main_mask.py --model SDCNet2DMaskStandalone --dataset MaskFrameLoader --train_file ./train/ --val_file ./val/
   ```

## 🎯 Key Requirements

- **Naming**: Mask directories must end with `_binary_ground_truth`
- **Correspondence**: Identical filenames between frames and masks
- **Format**: Binary masks with values 0 (background) and 255 (object)
- **Minimum**: At least 4 frames per video (for sequence_length=3)

## 📚 Documentation Files

- `../DATASET_STRUCTURE_GUIDE.md` - **Complete technical guide**
- `EXAMPLE_STRUCTURE.md` - **Concrete structure example**
- `../dataset_preparation_scripts/` - **Helper scripts and tools**

The structure is ready - just add your data following the documented format!
