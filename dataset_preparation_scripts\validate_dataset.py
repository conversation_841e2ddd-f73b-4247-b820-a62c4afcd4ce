#!/usr/bin/env python
"""
Dataset Validation Script for SDCNet2D

This script validates the dataset structure and reports any issues.

Usage:
    python validate_dataset.py --dataset_root ./dataset
"""

import argparse
import os
import cv2
import numpy as np
from pathlib import Path
import natsort

def validate_dataset_structure(dataset_root):
    """Validate the overall dataset structure."""
    print("🔍 Validating dataset structure...")
    
    required_dirs = [
        "train/X", "train/Y",
        "val/X", "val/Y", 
        "test/X", "test/Y"
    ]
    
    issues = []
    
    for dir_path in required_dirs:
        full_path = os.path.join(dataset_root, dir_path)
        if not os.path.exists(full_path):
            issues.append(f"Missing directory: {dir_path}")
        else:
            print(f"  ✅ Found: {dir_path}")
    
    if issues:
        print("  ❌ Structure issues found:")
        for issue in issues:
            print(f"    - {issue}")
        return False
    
    print("  ✅ Dataset structure is valid")
    return True

def validate_video_directories(dataset_root, split):
    """Validate video directories in a specific split."""
    print(f"\n🎬 Validating {split} video directories...")
    
    x_dir = os.path.join(dataset_root, split, "X")
    y_dir = os.path.join(dataset_root, split, "Y")
    
    if not os.path.exists(x_dir) or not os.path.exists(y_dir):
        print(f"  ❌ Missing {split} directories")
        return False
    
    # Get video directories (exclude mask directories)
    video_dirs = []
    mask_dirs = []
    
    for item in os.listdir(x_dir):
        item_path = os.path.join(x_dir, item)
        if os.path.isdir(item_path):
            if item.endswith('_binary_ground_truth'):
                mask_dirs.append(item)
            else:
                video_dirs.append(item)
    
    video_dirs = natsort.natsorted(video_dirs)
    mask_dirs = natsort.natsorted(mask_dirs)
    
    print(f"  Found {len(video_dirs)} video directories")
    print(f"  Found {len(mask_dirs)} mask directories")
    
    issues = []
    valid_videos = 0
    
    for video_name in video_dirs:
        video_dir = os.path.join(x_dir, video_name)
        mask_dir = os.path.join(x_dir, f"{video_name}_binary_ground_truth")
        target_dir = os.path.join(y_dir, video_name)
        
        # Check if corresponding mask directory exists
        if not os.path.exists(mask_dir):
            issues.append(f"Missing mask directory for {video_name}")
            continue
        
        # Check if corresponding target directory exists
        if not os.path.exists(target_dir):
            issues.append(f"Missing target directory for {video_name}")
            continue
        
        # Validate individual video
        video_issues = validate_single_video(video_dir, mask_dir, target_dir, video_name)
        if video_issues:
            issues.extend(video_issues)
        else:
            valid_videos += 1
    
    print(f"  ✅ Valid videos: {valid_videos}/{len(video_dirs)}")
    
    if issues:
        print(f"  ❌ Issues found:")
        for issue in issues[:10]:  # Show first 10 issues
            print(f"    - {issue}")
        if len(issues) > 10:
            print(f"    ... and {len(issues) - 10} more issues")
        return False
    
    return True

def validate_single_video(video_dir, mask_dir, target_dir, video_name):
    """Validate a single video and its corresponding masks/targets."""
    issues = []
    
    # Get file lists
    include_ext = [".png", ".jpg", ".jpeg", ".bmp"]
    
    video_files = [f for f in os.listdir(video_dir) 
                   if any(f.lower().endswith(ext) for ext in include_ext)]
    mask_files = [f for f in os.listdir(mask_dir) 
                  if any(f.lower().endswith(ext) for ext in include_ext)]
    target_files = [f for f in os.listdir(target_dir) 
                    if any(f.lower().endswith(ext) for ext in include_ext)]
    
    video_files = natsort.natsorted(video_files)
    mask_files = natsort.natsorted(mask_files)
    target_files = natsort.natsorted(target_files)
    
    # Check file counts
    if len(video_files) != len(mask_files):
        issues.append(f"{video_name}: Frame count mismatch (frames: {len(video_files)}, masks: {len(mask_files)})")
    
    if len(video_files) != len(target_files):
        issues.append(f"{video_name}: Target count mismatch (frames: {len(video_files)}, targets: {len(target_files)})")
    
    # Check minimum sequence length
    min_length = 4  # For sequence_length=3
    if len(video_files) < min_length:
        issues.append(f"{video_name}: Too few frames ({len(video_files)} < {min_length})")
    
    # Check filename correspondence
    if video_files != mask_files:
        issues.append(f"{video_name}: Frame and mask filenames don't match")
    
    if video_files != target_files:
        issues.append(f"{video_name}: Frame and target filenames don't match")
    
    # Validate a few sample images
    if len(video_files) > 0 and len(issues) == 0:
        sample_indices = [0, len(video_files)//2, len(video_files)-1]
        for idx in sample_indices:
            if idx < len(video_files):
                frame_path = os.path.join(video_dir, video_files[idx])
                mask_path = os.path.join(mask_dir, mask_files[idx])
                
                # Check frame
                frame = cv2.imread(frame_path)
                if frame is None:
                    issues.append(f"{video_name}: Cannot read frame {video_files[idx]}")
                    continue
                
                # Check mask
                mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
                if mask is None:
                    issues.append(f"{video_name}: Cannot read mask {mask_files[idx]}")
                    continue
                
                # Check mask format
                unique_values = np.unique(mask)
                if not (len(unique_values) <= 2 and all(v in [0, 255] for v in unique_values)):
                    issues.append(f"{video_name}: Mask {mask_files[idx]} is not binary (values: {unique_values})")
                
                # Check dimensions
                if frame.shape[:2] != mask.shape[:2]:
                    issues.append(f"{video_name}: Frame and mask dimension mismatch ({frame.shape[:2]} vs {mask.shape[:2]})")
    
    return issues

def generate_dataset_report(dataset_root):
    """Generate a comprehensive dataset report."""
    print("\n📊 Generating dataset report...")
    
    total_videos = 0
    total_frames = 0
    
    for split in ["train", "val", "test"]:
        x_dir = os.path.join(dataset_root, split, "X")
        if not os.path.exists(x_dir):
            continue
        
        video_dirs = [item for item in os.listdir(x_dir) 
                     if os.path.isdir(os.path.join(x_dir, item)) 
                     and not item.endswith('_binary_ground_truth')]
        
        split_frames = 0
        for video_name in video_dirs:
            video_dir = os.path.join(x_dir, video_name)
            include_ext = [".png", ".jpg", ".jpeg", ".bmp"]
            frames = [f for f in os.listdir(video_dir) 
                     if any(f.lower().endswith(ext) for ext in include_ext)]
            split_frames += len(frames)
        
        total_videos += len(video_dirs)
        total_frames += split_frames
        
        print(f"  {split.upper()}: {len(video_dirs)} videos, {split_frames} frames")
    
    print(f"  TOTAL: {total_videos} videos, {total_frames} frames")
    
    if total_frames > 0:
        print(f"  Average frames per video: {total_frames/total_videos:.1f}")

def main():
    parser = argparse.ArgumentParser(description="Validate SDCNet2D dataset structure")
    parser.add_argument("--dataset_root", default="./dataset", help="Root directory of dataset")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.dataset_root):
        print(f"❌ Dataset root not found: {args.dataset_root}")
        return False
    
    print(f"🔍 Validating dataset at: {args.dataset_root}")
    
    # Validate structure
    structure_valid = validate_dataset_structure(args.dataset_root)
    
    # Validate each split
    all_valid = structure_valid
    for split in ["train", "val", "test"]:
        split_valid = validate_video_directories(args.dataset_root, split)
        all_valid = all_valid and split_valid
    
    # Generate report
    generate_dataset_report(args.dataset_root)
    
    # Final result
    print(f"\n{'='*50}")
    if all_valid:
        print("🎉 Dataset validation PASSED! Ready for training.")
    else:
        print("❌ Dataset validation FAILED! Please fix the issues above.")
    print(f"{'='*50}")
    
    return all_valid

if __name__ == "__main__":
    main()
