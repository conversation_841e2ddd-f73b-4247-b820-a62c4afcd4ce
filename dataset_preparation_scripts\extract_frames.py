#!/usr/bin/env python
"""
Frame Extraction Script for SDCNet2D Dataset Preparation

This script extracts frames from video files and organizes them according to
the SDCNet2D dataset structure.

Usage:
    python extract_frames.py --input_video video.mp4 --output_dir dataset/train/X/video001 --fps 30
"""

import argparse
import os
import cv2
from pathlib import Path

def extract_frames(video_path, output_dir, fps=None, start_frame=0, max_frames=None):
    """
    Extract frames from video file.
    
    Args:
        video_path: Path to input video file
        output_dir: Directory to save extracted frames
        fps: Target FPS (None to use original)
        start_frame: Frame to start extraction from
        max_frames: Maximum number of frames to extract
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise ValueError(f"Cannot open video: {video_path}")
    
    # Get video properties
    original_fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Video properties:")
    print(f"  Original FPS: {original_fps}")
    print(f"  Total frames: {total_frames}")
    print(f"  Duration: {total_frames/original_fps:.2f} seconds")
    
    # Calculate frame sampling
    if fps is None:
        frame_step = 1
    else:
        frame_step = max(1, int(original_fps / fps))
    
    print(f"  Extracting every {frame_step} frame(s)")
    
    # Extract frames
    frame_count = 0
    extracted_count = 0
    
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Check if we should extract this frame
        if (frame_count - start_frame) % frame_step == 0:
            # Save frame
            frame_filename = f"{extracted_count + 1:06d}.png"
            frame_path = os.path.join(output_dir, frame_filename)
            cv2.imwrite(frame_path, frame)
            
            extracted_count += 1
            
            if extracted_count % 100 == 0:
                print(f"  Extracted {extracted_count} frames...")
            
            # Check max frames limit
            if max_frames and extracted_count >= max_frames:
                break
        
        frame_count += 1
    
    cap.release()
    
    print(f"Extraction complete: {extracted_count} frames saved to {output_dir}")
    return extracted_count

def main():
    parser = argparse.ArgumentParser(description="Extract frames from video for SDCNet2D dataset")
    parser.add_argument("--input_video", required=True, help="Path to input video file")
    parser.add_argument("--output_dir", required=True, help="Output directory for frames")
    parser.add_argument("--fps", type=float, help="Target FPS (default: use original)")
    parser.add_argument("--start_frame", type=int, default=0, help="Frame to start extraction from")
    parser.add_argument("--max_frames", type=int, help="Maximum number of frames to extract")
    
    args = parser.parse_args()
    
    # Validate input
    if not os.path.exists(args.input_video):
        raise FileNotFoundError(f"Input video not found: {args.input_video}")
    
    # Extract frames
    extract_frames(
        video_path=args.input_video,
        output_dir=args.output_dir,
        fps=args.fps,
        start_frame=args.start_frame,
        max_frames=args.max_frames
    )

if __name__ == "__main__":
    main()
