@echo off
echo ========================================
echo SDCNet2D Quick Test Training
echo ========================================
echo.
echo This script will run a quick 2-epoch training test to verify:
echo - Model loads correctly
echo - Dataset loads correctly  
echo - Training loop works without errors
echo - Loss computation is stable
echo - Model can save/load checkpoints
echo.

REM Check if virtual environment is activated
python -c "import sys; print('Virtual env:', hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))" 2>nul
if errorlevel 1 (
    echo WARNING: Python not found or virtual environment not activated
    echo Please activate your sdcnet_env virtual environment first:
    echo   conda activate sdcnet_env
    echo.
    pause
    exit /b 1
)

REM Check if dataset exists
if not exist "dataset\train" (
    echo ERROR: Training dataset not found at dataset\train
    echo Please ensure your dataset is properly structured:
    echo   dataset\
    echo   ├── train\
    echo   │   ├── X\  ^(frames^)
    echo   │   └── Y\  ^(masks^)
    echo   └── val\
    echo       ├── X\  ^(frames^)
    echo       └── Y\  ^(masks^)
    echo.
    pause
    exit /b 1
)

if not exist "dataset\val" (
    echo ERROR: Validation dataset not found at dataset\val
    echo Please ensure your dataset is properly structured.
    echo.
    pause
    exit /b 1
)

echo Starting quick test training...
echo.

REM Run the quick test with SDCNet2DMaskStandalone
python quick_test_training.py --model SDCNet2DMaskStandalone

if errorlevel 1 (
    echo.
    echo ❌ Quick test FAILED!
    echo Check the error messages above for details.
) else (
    echo.
    echo 🎉 Quick test PASSED!
    echo Your SDCNet2D setup is working correctly.
    echo You can now proceed with full training using:
    echo   python main_mask.py --model SDCNet2DMaskStandalone
)

echo.
pause
