# File: populate_dataset-2.py
"""
Importa DAVIS-2017 nel dataset SDCNet (320×256) evitando duplicati.
Layout finale:
dataset/{train,val,test}/
 ├─ X/<video_i>/00012.png                ← frame RGB
 ├─ X/<video_i>_binary_ground_truth/00012.png  ← maschera (se esiste)
 └─ Y/<video_i>/00012.png                ← frame RGB (target)

Uso (PowerShell):
conda activate sdcnet_env; cd "C:/…/sdcnet"; python populate_dataset-2.py ^
    --frames-root "C:/…/DAVIS-2017/DAVIS/JPEGImages/480p" ^
    --masks-root  "C:/…/DAVIS-2017/DAVIS/Annotations/480p" ^
    --out-root    "C:/…/sdcnet/dataset" ^
    --width 320 --height 256
"""

import argparse, random, sys
from collections import defaultdict
from pathlib import Path

import numpy as np
from PIL import Image
from tqdm import tqdm

TARGET_W, TARGET_H = 320, 256
RNG_SEED           = 42
SPLIT_RATIO        = (0.70, 0.15, 0.15)   # train / val / test


# ───────────────────────────── utils ────────────────────────────── #
def rsz_crop(img: Image.Image, is_mask=False) -> Image.Image:
    w0, h0 = img.size
    s = max(TARGET_W / w0, TARGET_H / h0)
    nw, nh = int(round(w0 * s)), int(round(h0 * s))
    interp = Image.NEAREST if is_mask else Image.BILINEAR
    img = img.resize((nw, nh), interp)
    l, t = (nw - TARGET_W) // 2, (nh - TARGET_H) // 2
    return img.crop((l, t, l + TARGET_W, t + TARGET_H))


def save(img: Image.Image, path: Path):
    path.parent.mkdir(parents=True, exist_ok=True)
    if not path.exists():
        img.save(path)


def instance_done(name: str, root: Path) -> bool:
    return any((root / sp / "Y" / name).exists() for sp in ("train", "val", "test"))


# ─────────────────── indicizzazione e split ─────────────────────── #
def index_masks(mask_root: Path):
    vids = defaultdict(list)
    for p in mask_root.rglob("*.png"):
        vids[p.parent.name].append(p)
    for v in vids:
        vids[v].sort()
    return vids


def split_videos(names):
    random.seed(RNG_SEED)
    v = list(names); random.shuffle(v)
    n = len(v); nt = int(SPLIT_RATIO[0]*n); nv = int(SPLIT_RATIO[1]*n)
    m = {x: "train" for x in v[:nt]}
    m.update({x: "val"   for x in v[nt:nt+nv]})
    m.update({x: "test"  for x in v[nt+nv:]})
    return m


# ────────────────────── process video/istanza ───────────────────── #
def process_video(video, frame_files, mask_files, split, out_root):
    first = np.asarray(Image.open(mask_files[0]))
    obj_ids = [i for i in np.unique(first) if i != 0]
    names = [video] if len(obj_ids) == 1 else [f"{video}_{i}" for i in obj_ids]

    # cache RGB ridimensionati
    rgb_cache = {f.stem: rsz_crop(Image.open(f)) for f in frame_files}

    for obj, vname in zip(obj_ids, names):
        vname = str(vname)
        if instance_done(vname, out_root):
            print(f"  • {vname} già presente – skip")
            continue

        print(f"▶ {vname:35} ➜ {split}")

        # maschere binarie resize+crop (solo dove esistono)
        masks = {}
        for p in mask_files:
            raw = np.asarray(Image.open(p))
            m_bin = np.where(raw == obj, 255, 0).astype(np.uint8)
            masks[p.stem] = rsz_crop(Image.fromarray(m_bin), is_mask=True)

        # salva tutti i frame RGB
        for idx_str, rgb in rgb_cache.items():
            save(rgb, out_root / split / "X" / vname / f"{idx_str}.png")
            save(rgb, out_root / split / "Y" / vname / f"{idx_str}.png")

        # salva le maschere disponibili
        for idx_str, m in masks.items():
            save(m, out_root / split / "X" /
                      f"{vname}_binary_ground_truth" / f"{idx_str}.png")


# ───────────────────────────── main ─────────────────────────────── #
def main():
    global TARGET_W, TARGET_H
    ap = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    ap.add_argument("--frames-root", required=True)
    ap.add_argument("--masks-root",  required=True)
    ap.add_argument("--out-root",    required=True)
    ap.add_argument("--width",  type=int, default=TARGET_W)
    ap.add_argument("--height", type=int, default=TARGET_H)
    args = ap.parse_args()
    TARGET_W, TARGET_H = args.width, args.height

    frames_root = Path(args.frames_root)
    masks_root  = Path(args.masks_root)
    out_root    = Path(args.out_root); out_root.mkdir(exist_ok=True)

    vids = index_masks(masks_root)
    split_map = split_videos(vids.keys())
    print(f"· Video DAVIS-2017: {len(vids)} "
          f"(train/val/test = {list(split_map.values()).count('train')}/"
          f"{list(split_map.values()).count('val')}/"
          f"{list(split_map.values()).count('test')})")

    for vid, mfiles in vids.items():
        ffiles = sorted((frames_root / vid).glob("*.jpg")) + \
                 sorted((frames_root / vid).glob("*.png"))
        if not ffiles:
            print(f"⚠ Frame RGB mancanti: {vid} – skip")
            continue
        process_video(vid, ffiles, mfiles, split_map[vid], out_root)

    print("\n✅ Import DAVIS-2017 completato:", out_root)


if __name__ == "__main__":
    sys.exit(main())
