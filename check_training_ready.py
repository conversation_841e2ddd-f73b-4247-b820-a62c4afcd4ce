#!/usr/bin/env python
"""
Pre-Training Check Script

This script performs comprehensive checks to ensure everything is ready for training.
Run this before starting training to catch any issues early.

Usage:
    python check_training_ready.py
"""

import os
import sys
import torch
import importlib.util
from pathlib import Path

def check_pytorch_setup():
    """Check PyTorch installation and GPU availability."""
    print("🔍 Checking PyTorch Setup...")
    
    print(f"  PyTorch version: {torch.__version__}")
    print(f"  CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"  CUDA version: {torch.version.cuda}")
        print(f"  GPU count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"  GPU {i}: {props.name} ({props.total_memory / 1e9:.1f} GB)")
    else:
        print("  ⚠️ CUDA not available - training will use CPU (slower)")
    
    return True

def check_model_availability():
    """Check which model versions are available."""
    print("\n🤖 Checking Model Availability...")
    
    models_available = []
    
    # Check standalone model (recommended)
    try:
        spec = importlib.util.spec_from_file_location(
            "sdc_net2d_mask_standalone", 
            "models/sdc_net2d_mask_standalone.py"
        )
        standalone_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(standalone_module)
        print("  ✅ SDCNet2DMaskStandalone (RECOMMENDED)")
        models_available.append("SDCNet2DMaskStandalone")
    except Exception as e:
        print(f"  ❌ SDCNet2DMaskStandalone: {e}")
    
    # Check native model
    try:
        from models.sdc_net2d_mask_native import SDCNet2DMaskNative
        print("  ✅ SDCNet2DMaskNative")
        models_available.append("SDCNet2DMaskNative")
    except Exception as e:
        print(f"  ❌ SDCNet2DMaskNative: {e}")
    
    # Check CUDA model
    try:
        from models.sdc_net2d_mask import SDCNet2DMask
        print("  ✅ SDCNet2DMask")
        models_available.append("SDCNet2DMask")
    except Exception as e:
        print(f"  ❌ SDCNet2DMask: {e}")
    
    if not models_available:
        print("  ❌ No models available!")
        return False
    
    print(f"  ✅ {len(models_available)} model(s) available")
    return True

def check_dataset_structure():
    """Check dataset structure and content."""
    print("\n📁 Checking Dataset Structure...")
    
    dataset_root = "./dataset"
    if not os.path.exists(dataset_root):
        print(f"  ❌ Dataset directory not found: {dataset_root}")
        return False
    
    # Check required directories
    required_dirs = ["train/X", "train/Y", "val/X", "val/Y"]
    for dir_path in required_dirs:
        full_path = os.path.join(dataset_root, dir_path)
        if not os.path.exists(full_path):
            print(f"  ❌ Missing directory: {dir_path}")
            return False
        print(f"  ✅ Found: {dir_path}")
    
    # Check content
    train_x = os.path.join(dataset_root, "train/X")
    val_x = os.path.join(dataset_root, "val/X")
    
    # Count videos in training set
    train_videos = [d for d in os.listdir(train_x) 
                   if os.path.isdir(os.path.join(train_x, d)) 
                   and not d.endswith('_binary_ground_truth')]
    
    # Count videos in validation set
    val_videos = [d for d in os.listdir(val_x) 
                 if os.path.isdir(os.path.join(val_x, d)) 
                 and not d.endswith('_binary_ground_truth')]
    
    print(f"  Training videos: {len(train_videos)}")
    print(f"  Validation videos: {len(val_videos)}")
    
    if len(train_videos) == 0:
        print("  ❌ No training videos found!")
        return False
    
    if len(val_videos) == 0:
        print("  ⚠️ No validation videos found - consider adding some")
    
    return True

def check_sample_video():
    """Check a sample video for proper format."""
    print("\n🎬 Checking Sample Video...")
    
    train_x = "./dataset/train/X"
    if not os.path.exists(train_x):
        print("  ❌ Training directory not found")
        return False
    
    # Find first video
    videos = [d for d in os.listdir(train_x) 
             if os.path.isdir(os.path.join(train_x, d)) 
             and not d.endswith('_binary_ground_truth')]
    
    if not videos:
        print("  ❌ No videos found")
        return False
    
    video_name = videos[0]
    video_dir = os.path.join(train_x, video_name)
    mask_dir = os.path.join(train_x, f"{video_name}_binary_ground_truth")
    
    print(f"  Checking video: {video_name}")
    
    # Check mask directory exists
    if not os.path.exists(mask_dir):
        print(f"  ❌ Mask directory not found: {video_name}_binary_ground_truth")
        return False
    
    # Count frames and masks
    include_ext = [".png", ".jpg", ".jpeg", ".bmp"]
    frames = [f for f in os.listdir(video_dir) 
             if any(f.lower().endswith(ext) for ext in include_ext)]
    masks = [f for f in os.listdir(mask_dir) 
            if any(f.lower().endswith(ext) for ext in include_ext)]
    
    print(f"  Frames: {len(frames)}")
    print(f"  Masks: {len(masks)}")
    
    if len(frames) != len(masks):
        print("  ❌ Frame and mask count mismatch!")
        return False
    
    if len(frames) < 4:
        print("  ❌ Too few frames (need at least 4 for sequence_length=3)")
        return False
    
    # Check first frame and mask
    if frames and masks:
        import cv2
        import numpy as np
        
        frame_path = os.path.join(video_dir, sorted(frames)[0])
        mask_path = os.path.join(mask_dir, sorted(masks)[0])
        
        # Load frame
        frame = cv2.imread(frame_path)
        if frame is None:
            print(f"  ❌ Cannot read frame: {sorted(frames)[0]}")
            return False
        
        # Load mask
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        if mask is None:
            print(f"  ❌ Cannot read mask: {sorted(masks)[0]}")
            return False
        
        # Check mask format
        unique_values = np.unique(mask)
        if not (len(unique_values) <= 2 and all(v in [0, 255] for v in unique_values)):
            print(f"  ❌ Mask is not binary (values: {unique_values})")
            return False
        
        # Check dimensions
        if frame.shape[:2] != mask.shape[:2]:
            print(f"  ❌ Frame and mask dimension mismatch")
            return False
        
        print(f"  ✅ Sample check passed")
        print(f"    Frame size: {frame.shape}")
        print(f"    Mask values: {unique_values}")
    
    return True

def check_dependencies():
    """Check required dependencies."""
    print("\n📦 Checking Dependencies...")
    
    required_packages = [
        'torch', 'torchvision', 'cv2', 'numpy', 
        'natsort', 'tensorboardX', 'skimage', 'tqdm'
    ]
    
    missing = []
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'skimage':
                import skimage
            else:
                __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"  ❌ Missing packages: {missing}")
        print("  Install with: pip install " + " ".join(missing))
        return False
    
    return True

def estimate_training_time():
    """Estimate training time based on dataset size and hardware."""
    print("\n⏱️ Training Time Estimation...")
    
    # Count total frames
    train_x = "./dataset/train/X"
    if not os.path.exists(train_x):
        return
    
    total_frames = 0
    videos = [d for d in os.listdir(train_x) 
             if os.path.isdir(os.path.join(train_x, d)) 
             and not d.endswith('_binary_ground_truth')]
    
    for video in videos[:10]:  # Sample first 10 videos
        video_dir = os.path.join(train_x, video)
        include_ext = [".png", ".jpg", ".jpeg", ".bmp"]
        frames = [f for f in os.listdir(video_dir) 
                 if any(f.lower().endswith(ext) for ext in include_ext)]
        total_frames += len(frames)
    
    if len(videos) > 10:
        # Estimate total based on sample
        avg_frames = total_frames / 10
        total_frames = int(avg_frames * len(videos))
    
    print(f"  Total videos: {len(videos)}")
    print(f"  Estimated total frames: {total_frames}")
    
    # Rough time estimates (very approximate)
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        if gpu_memory >= 8:
            time_per_epoch = total_frames * 0.1  # seconds
        else:
            time_per_epoch = total_frames * 0.2
    else:
        time_per_epoch = total_frames * 1.0  # CPU is much slower
    
    epochs = 500
    total_time = time_per_epoch * epochs / 3600  # hours
    
    print(f"  Estimated time per epoch: {time_per_epoch/60:.1f} minutes")
    print(f"  Estimated total training time: {total_time:.1f} hours")

def main():
    print("🔍 SDCNet2D Training Readiness Check")
    print("=" * 50)
    
    checks = [
        check_pytorch_setup,
        check_dependencies,
        check_model_availability,
        check_dataset_structure,
        check_sample_video,
    ]
    
    all_passed = True
    for check in checks:
        try:
            result = check()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"  ❌ Check failed: {e}")
            all_passed = False
    
    estimate_training_time()
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL CHECKS PASSED! Ready to start training.")
        print("\nNext steps:")
        print("1. Quick test: python start_training.py --quick_test")
        print("2. Full training: python start_training.py")
    else:
        print("❌ SOME CHECKS FAILED! Please fix the issues above.")
    print("=" * 50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
