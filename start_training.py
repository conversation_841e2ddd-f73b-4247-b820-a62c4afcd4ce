#!/usr/bin/env python
"""
SDCNet2D with Mask Support - Training Launcher

This script provides an easy way to start training with optimal parameters.
It automatically detects the best available model version and configures training.

Usage:
    python start_training.py [--quick_test] [--resume checkpoint.pth]
"""

import argparse
import os
import sys
import subprocess
import torch

def check_environment():
    """Check if the environment is properly configured."""
    print("🔍 Checking environment...")

    # Check PyTorch
    print(f"  PyTorch version: {torch.__version__}")
    print(f"  CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"  GPU: {torch.cuda.get_device_name(0)}")
        print(f"  GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

    # Check dataset
    dataset_path = "./dataset"
    if not os.path.exists(dataset_path):
        print(f"  ❌ Dataset not found at {dataset_path}")
        return False

    # Check required directories
    required_dirs = ["train/X", "train/Y", "val/X", "val/Y"]
    for dir_path in required_dirs:
        full_path = os.path.join(dataset_path, dir_path)
        if not os.path.exists(full_path):
            print(f"  ❌ Missing directory: {dir_path}")
            return False

        # Count videos in X directories
        if dir_path.endswith("/X"):
            videos = [d for d in os.listdir(full_path)
                     if os.path.isdir(os.path.join(full_path, d))
                     and not d.endswith('_binary_ground_truth')]
            print(f"  ✅ {dir_path}: {len(videos)} videos")

    print("  ✅ Environment check passed!")
    return True

def detect_best_model():
    """Detect the best available model version."""
    print("🔍 Detecting best available model...")

    # Try standalone first (recommended)
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "sdc_net2d_mask_standalone",
            "models/sdc_net2d_mask_standalone.py"
        )
        standalone_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(standalone_module)
        print("  ✅ SDCNet2DMaskStandalone available (RECOMMENDED)")
        return "SDCNet2DMaskStandalone"
    except Exception as e:
        print(f"  ❌ Standalone model failed: {e}")

    # Try native version
    try:
        from models.sdc_net2d_mask_native import SDCNet2DMaskNative
        print("  ✅ SDCNet2DMaskNative available")
        return "SDCNet2DMaskNative"
    except Exception as e:
        print(f"  ❌ Native model failed: {e}")

    # Try CUDA version
    try:
        from models.sdc_net2d_mask import SDCNet2DMask
        print("  ✅ SDCNet2DMask available")
        return "SDCNet2DMask"
    except Exception as e:
        print(f"  ❌ CUDA model failed: {e}")

    print("  ❌ No model versions available!")
    return None

def get_optimal_parameters():
    """Get optimal training parameters based on available hardware."""
    print("🔧 Configuring optimal parameters...")

    # Base parameters
    params = {
        'model': 'SDCNet2DMaskStandalone',
        'dataset': 'MaskFrameLoader',
        'train_file': './dataset/train/',
        'val_file': './dataset/val/',
        'sequence_length': 3,
        'rgb_max': 255.0,
        'crop_size': [256, 256],
        'epochs': 500,
        'learning_rate': 1e-4,
        'mask_weight': 0.2,
        'save_freq': 50,
        'val_freq': 10,
        'print_freq': 10,
    }

    # Adjust based on GPU memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"  GPU memory: {gpu_memory:.1f} GB")

        # Optimized for 320x256 images
        if gpu_memory >= 8:
            params['batch_size'] = 8
            params['crop_size'] = [256, 256]  # Perfect fit for 320x256
            print("  ✅ High-end GPU: batch_size=8, crop_size=256x256 (optimized for 320x256)")
        elif gpu_memory >= 4:
            params['batch_size'] = 6
            params['crop_size'] = [224, 224]  # Good balance
            print("  ✅ Mid-range GPU: batch_size=6, crop_size=224x224 (optimized for 320x256)")
        else:
            params['batch_size'] = 4
            params['crop_size'] = [192, 192]  # Conservative
            print("  ⚠️ Low GPU memory: batch_size=4, crop_size=192x192 (optimized for 320x256)")
    else:
        params['batch_size'] = 2
        params['crop_size'] = [224, 224]
        print("  ⚠️ CPU only: batch_size=2, crop_size=224x224")

    return params

def create_training_command(params, quick_test=False, resume=None):
    """Create the training command."""
    cmd = [
        sys.executable, 'main_mask_standalone.py',
        '--train_file', params['train_file'],
        '--val_file', params['val_file'],
        '--sequence_length', str(params['sequence_length']),
        '--rgb_max', str(params['rgb_max']),
        '--crop_size', str(params['crop_size'][0]), str(params['crop_size'][1]),
        '--batch_size', str(params['batch_size']),
        '--learning_rate', str(params['learning_rate']),
        '--mask_weight', str(params['mask_weight']),
        '--save_freq', str(params['save_freq']),
        '--val_freq', str(params['val_freq']),
        '--print_freq', str(params['print_freq']),
    ]

    if quick_test:
        cmd.extend(['--epochs', '2', '--val_n_batches', '2'])
        print("  🚀 Quick test mode: 2 epochs, 2 validation batches")
    else:
        cmd.extend(['--epochs', str(params['epochs'])])
        print(f"  🚀 Full training: {params['epochs']} epochs")

    if resume:
        cmd.extend(['--resume', resume])
        print(f"  🔄 Resuming from: {resume}")

    return cmd

def main():
    parser = argparse.ArgumentParser(description="Start SDCNet2D mask training")
    parser.add_argument('--quick_test', action='store_true',
                       help='Run quick test (2 epochs)')
    parser.add_argument('--resume', type=str,
                       help='Resume from checkpoint')
    parser.add_argument('--batch_size', type=int,
                       help='Override batch size')
    parser.add_argument('--epochs', type=int,
                       help='Override number of epochs')

    args = parser.parse_args()

    print("🎯 SDCNet2D Mask Training Launcher")
    print("=" * 50)

    # Check environment
    if not check_environment():
        print("❌ Environment check failed. Please fix the issues above.")
        return False

    # Detect best model
    best_model = detect_best_model()
    if not best_model:
        print("❌ No working model found. Please check your installation.")
        return False

    # Get optimal parameters
    params = get_optimal_parameters()
    params['model'] = best_model

    # Override parameters if specified
    if args.batch_size:
        params['batch_size'] = args.batch_size
        print(f"  🔧 Override batch_size: {args.batch_size}")

    if args.epochs:
        params['epochs'] = args.epochs
        print(f"  🔧 Override epochs: {args.epochs}")

    # Create training command
    cmd = create_training_command(params, args.quick_test, args.resume)

    print("\n🚀 Starting training...")
    print("Command:", ' '.join(cmd))
    print("=" * 50)

    # Start training
    try:
        result = subprocess.run(cmd, cwd=os.getcwd())
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\n⏹️ Training interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
