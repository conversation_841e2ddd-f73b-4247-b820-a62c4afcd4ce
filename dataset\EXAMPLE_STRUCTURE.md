# Example Dataset Structure

This file shows exactly how your dataset should look when properly organized.

## 📁 Complete Structure Example

```
dataset/
├── train/
│   ├── X/                                    # Input data
│   │   ├── kitchen_scene_01/                 # Video 1 frames
│   │   │   ├── 000001.png                    # Frame 1
│   │   │   ├── 000002.png                    # Frame 2
│   │   │   ├── 000003.png                    # Frame 3
│   │   │   ├── 000004.png                    # Frame 4
│   │   │   ├── 000005.png                    # Frame 5
│   │   │   └── ...                           # More frames
│   │   ├── kitchen_scene_01_binary_ground_truth/  # Video 1 masks
│   │   │   ├── 000001.png                    # Mask 1 (binary: 0/255)
│   │   │   ├── 000002.png                    # Mask 2 (binary: 0/255)
│   │   │   ├── 000003.png                    # Mask 3 (binary: 0/255)
│   │   │   ├── 000004.png                    # Mask 4 (binary: 0/255)
│   │   │   ├── 000005.png                    # Mask 5 (binary: 0/255)
│   │   │   └── ...                           # More masks
│   │   ├── person_walking_02/                # Video 2 frames
│   │   │   ├── 000001.png
│   │   │   ├── 000002.png
│   │   │   └── ...
│   │   ├── person_walking_02_binary_ground_truth/  # Video 2 masks
│   │   │   ├── 000001.png
│   │   │   ├── 000002.png
│   │   │   └── ...
│   │   ├── car_driving_03/                   # Video 3 frames
│   │   ├── car_driving_03_binary_ground_truth/     # Video 3 masks
│   │   └── ...                               # More videos
│   └── Y/                                    # Target data
│       ├── kitchen_scene_01/                 # Video 1 targets
│       │   ├── 000001.png                    # Target frame 1
│       │   ├── 000002.png                    # Target frame 2
│       │   ├── 000003.png                    # Target frame 3
│       │   ├── 000004.png                    # Target frame 4
│       │   ├── 000005.png                    # Target frame 5
│       │   └── ...                           # More targets
│       ├── person_walking_02/                # Video 2 targets
│       ├── car_driving_03/                   # Video 3 targets
│       └── ...                               # More videos
├── val/                                      # Validation split
│   ├── X/
│   │   ├── val_kitchen_scene_04/
│   │   ├── val_kitchen_scene_04_binary_ground_truth/
│   │   ├── val_person_walking_05/
│   │   ├── val_person_walking_05_binary_ground_truth/
│   │   └── ...
│   └── Y/
│       ├── val_kitchen_scene_04/
│       ├── val_person_walking_05/
│       └── ...
└── test/                                     # Test split
    ├── X/
    │   ├── test_kitchen_scene_06/
    │   ├── test_kitchen_scene_06_binary_ground_truth/
    │   ├── test_person_walking_07/
    │   ├── test_person_walking_07_binary_ground_truth/
    │   └── ...
    └── Y/
        ├── test_kitchen_scene_06/
        ├── test_person_walking_07/
        └── ...
```

## 🎯 Key Points

### 1. Naming Convention
- **Video directories**: Any descriptive name (e.g., `kitchen_scene_01`)
- **Mask directories**: Video name + `_binary_ground_truth` (e.g., `kitchen_scene_01_binary_ground_truth`)
- **Frame files**: Sequential numbering with leading zeros (e.g., `000001.png`)

### 2. File Correspondence
- **Identical filenames**: Frame and mask files must have exactly the same names
- **Same count**: Each video directory must have the same number of files as its mask directory
- **Same format**: All files should use the same image format (PNG recommended)

### 3. Data Requirements
- **Minimum frames**: At least `sequence_length + 1` frames per video (default: 4 frames)
- **Binary masks**: Mask values must be exactly 0 (background) and 255 (object)
- **Consistent resolution**: All frames in a video should have the same resolution

### 4. Directory Structure
- **X directories**: Contain both frames and masks for input
- **Y directories**: Contain target frames only
- **Three splits**: train, val, test with identical internal structure

## 📊 Example Statistics

For a typical dataset:

```
Dataset Statistics:
├── Training: 1000 videos, 50,000 frames
├── Validation: 200 videos, 10,000 frames
└── Test: 200 videos, 10,000 frames

Total: 1,400 videos, 70,000 frames
Average: 50 frames per video
```

## ✅ Validation Checklist

Before training, ensure:

- [ ] All video directories have corresponding `_binary_ground_truth` directories
- [ ] Frame and mask files have identical names
- [ ] All masks are binary (0/255 values only)
- [ ] Each video has at least 4 frames (for sequence_length=3)
- [ ] X and Y directories have matching video names
- [ ] File formats are consistent (PNG recommended)
- [ ] No missing or corrupted files

## 🚀 Ready to Use

Once your dataset matches this structure, you can:

1. **Validate**: `python dataset_preparation_scripts/validate_dataset.py`
2. **Train**: `python main_mask.py --model SDCNet2DMaskStandalone --dataset MaskFrameLoader --train_file ./dataset/train/ --val_file ./dataset/val/`

This structure ensures optimal compatibility with SDCNet2D mask extension!
