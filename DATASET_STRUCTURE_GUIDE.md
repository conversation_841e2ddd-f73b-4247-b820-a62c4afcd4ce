# SDCNet2D Dataset Structure Guide

## 📁 Overview

This guide explains the optimal dataset structure for SDCNet2D with binary mask support. The structure is designed to be intuitive, scalable, and compatible with the `MaskFrameLoader`.

## 🏗️ Directory Structure

```
dataset/
├── train/
│   ├── X/                          # Input data (frames + masks)
│   │   ├── video001/               # RGB frames for video001
│   │   │   ├── 000001.png
│   │   │   ├── 000002.png
│   │   │   ├── 000003.png
│   │   │   └── ...
│   │   ├── video001_binary_ground_truth/  # Binary masks for video001
│   │   │   ├── 000001.png
│   │   │   ├── 000002.png
│   │   │   ├── 000003.png
│   │   │   └── ...
│   │   ├── video002/               # RGB frames for video002
│   │   ├── video002_binary_ground_truth/  # Binary masks for video002
│   │   └── ...
│   └── Y/                          # Target data (frames only)
│       ├── video001/               # Target frames for video001
│       │   ├── 000001.png
│       │   ├── 000002.png
│       │   └── ...
│       ├── video002/               # Target frames for video002
│       └── ...
├── val/                            # Validation split (same structure as train)
│   ├── X/
│   │   ├── val_video001/
│   │   ├── val_video001_binary_ground_truth/
│   │   └── ...
│   └── Y/
│       ├── val_video001/
│       └── ...
└── test/                           # Test split (same structure as train)
    ├── X/
    │   ├── test_video001/
    │   ├── test_video001_binary_ground_truth/
    │   └── ...
    └── Y/
        ├── test_video001/
        └── ...
```

## 📋 Naming Conventions

### 1. Video Directory Names
- **Format**: Any descriptive name (e.g., `video001`, `scene_kitchen_01`, `person_walking`)
- **Allowed Characters**: Letters, numbers, underscores, hyphens
- **Case Sensitivity**: Consistent naming recommended (e.g., all lowercase)

### 2. Mask Directory Names
- **Format**: `{video_name}_binary_ground_truth`
- **Example**: If video is `video001`, mask directory is `video001_binary_ground_truth`
- **Critical**: Must end with `_binary_ground_truth` for automatic detection

### 3. Frame File Names
- **Format**: Sequential numbering with leading zeros
- **Recommended**: `000001.png`, `000002.png`, `000003.png`, etc.
- **Alternative**: `frame_001.jpg`, `img_0001.png` (any consistent pattern)
- **Critical**: **Identical names** between frames and masks

### 4. File Extensions
- **Supported**: `.png`, `.jpg`, `.jpeg`, `.bmp`
- **Recommended**: `.png` for lossless quality
- **Masks**: Should be grayscale images (0=background, 255=object)

## 🎯 Data Requirements

### Frame Requirements
- **Resolution**: Any resolution (automatically handled by model)
- **Format**: RGB color images
- **Quality**: High quality recommended for better training
- **Consistency**: Same resolution within each video sequence

### Mask Requirements
- **Format**: Grayscale images (single channel)
- **Values**: Binary (0 for background, 255 for object of interest)
- **Resolution**: Should match corresponding frame resolution
- **Precision**: Accurate object boundaries for best results

### Sequence Requirements
- **Minimum Length**: `sequence_length + 1` frames per video
- **Default**: At least 4 frames (for sequence_length=3)
- **Recommended**: 10+ frames per video for better training
- **Temporal Consistency**: Smooth object motion between frames

## 📊 Data Split Recommendations

### Training Set (70-80%)
- **Purpose**: Model training and parameter optimization
- **Size**: Largest portion of your dataset
- **Diversity**: Include various scenarios, lighting, objects

### Validation Set (10-15%)
- **Purpose**: Hyperparameter tuning and model selection
- **Size**: Sufficient for reliable evaluation
- **Representation**: Representative of test scenarios

### Test Set (10-15%)
- **Purpose**: Final model evaluation
- **Size**: Adequate for statistical significance
- **Independence**: Never used during training or validation

## 🔧 Data Preparation Steps

### 1. Video Extraction
```bash
# Extract frames from video using ffmpeg
ffmpeg -i input_video.mp4 -vf fps=30 video001/%06d.png
```

### 2. Mask Generation
- Use segmentation tools (e.g., SAM, Detectron2, manual annotation)
- Ensure binary format (0/255 values)
- Maintain temporal consistency across frames

### 3. Quality Control
- Verify frame-mask correspondence
- Check for missing files
- Validate mask quality and coverage

### 4. Directory Organization
```bash
# Example organization script
mkdir -p dataset/train/X/video001
mkdir -p dataset/train/X/video001_binary_ground_truth
mkdir -p dataset/train/Y/video001

# Copy files to appropriate directories
cp frames/* dataset/train/X/video001/
cp masks/* dataset/train/X/video001_binary_ground_truth/
cp targets/* dataset/train/Y/video001/
```

## ✅ Validation Checklist

Before training, verify:

- [ ] **Directory Structure**: Follows the documented pattern
- [ ] **Naming Convention**: Mask directories end with `_binary_ground_truth`
- [ ] **File Correspondence**: Identical filenames between frames and masks
- [ ] **File Count**: Each video has sufficient frames (≥ sequence_length + 1)
- [ ] **Mask Format**: Binary grayscale images (0/255 values)
- [ ] **Frame Quality**: Good resolution and lighting
- [ ] **Data Splits**: Appropriate train/val/test distribution

## 🚀 Usage Example

Once your dataset is prepared:

```bash
# Test dataset loading
python -c "
from datasets.mask_frame_loader import MaskFrameLoader
import argparse

# Create mock args
args = argparse.Namespace()
args.sequence_length = 3
args.sample_rate = 1
args.crop_size = [256, 256]
args.start_index = 0
args.stride = 64

# Test dataset loading
dataset = MaskFrameLoader(args, 'dataset/train', is_training=True)
print(f'Dataset loaded successfully: {len(dataset)} sequences')
"

# Start training
python main_mask.py \
    --model SDCNet2DMaskStandalone \
    --dataset MaskFrameLoader \
    --train_file ./dataset/train/ \
    --val_file ./dataset/val/ \
    --sequence_length 3 \
    --batch_size 4
```

## 🔍 Troubleshooting

### Common Issues

#### "No valid sequences found"
- **Cause**: Incorrect directory structure or naming
- **Solution**: Verify mask directories end with `_binary_ground_truth`

#### "Mismatched file counts"
- **Cause**: Different number of frames vs masks
- **Solution**: Ensure identical filenames in frame and mask directories

#### "File not found" errors
- **Cause**: Missing files or incorrect paths
- **Solution**: Check file existence and path consistency

#### "Invalid mask format"
- **Cause**: Non-binary masks or wrong format
- **Solution**: Convert masks to grayscale with 0/255 values

### Debug Commands

```bash
# Check directory structure
find dataset/ -type d | head -20

# Count files in directories
find dataset/train/X/video001/ -name "*.png" | wc -l
find dataset/train/X/video001_binary_ground_truth/ -name "*.png" | wc -l

# Verify mask values
python -c "
import cv2
import numpy as np
mask = cv2.imread('dataset/train/X/video001_binary_ground_truth/000001.png', 0)
print(f'Mask shape: {mask.shape}')
print(f'Unique values: {np.unique(mask)}')
print(f'Value range: {mask.min()}-{mask.max()}')
"
```

## 📈 Best Practices

### Data Quality
- **High Resolution**: Use highest available resolution
- **Consistent Lighting**: Avoid extreme lighting changes
- **Smooth Motion**: Ensure temporal consistency
- **Clean Masks**: Accurate object boundaries

### Organization
- **Descriptive Names**: Use meaningful video names
- **Consistent Naming**: Follow the same pattern throughout
- **Backup Data**: Keep original data safe
- **Version Control**: Track dataset changes

### Scalability
- **Modular Structure**: Easy to add new videos
- **Batch Processing**: Automate data preparation
- **Quality Metrics**: Track data quality over time
- **Documentation**: Document data sources and processing

This structure ensures optimal compatibility with SDCNet2D mask extension while maintaining flexibility for future enhancements.
