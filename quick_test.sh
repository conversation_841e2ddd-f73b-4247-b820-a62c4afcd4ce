#!/bin/bash

echo "========================================"
echo "SDCNet2D Quick Test Training"
echo "========================================"
echo ""
echo "This script will run a quick 2-epoch training test to verify:"
echo "- Model loads correctly"
echo "- Dataset loads correctly"
echo "- Training loop works without errors"
echo "- Loss computation is stable"
echo "- Model can save/load checkpoints"
echo ""

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "ERROR: Python not found"
    echo "Please ensure Python is installed and in your PATH"
    exit 1
fi

# Check if dataset exists
if [ ! -d "dataset/train" ]; then
    echo "ERROR: Training dataset not found at dataset/train"
    echo "Please ensure your dataset is properly structured:"
    echo "  dataset/"
    echo "  ├── train/"
    echo "  │   ├── X/  (frames)"
    echo "  │   └── Y/  (masks)"
    echo "  └── val/"
    echo "      ├── X/  (frames)"
    echo "      └── Y/  (masks)"
    echo ""
    exit 1
fi

if [ ! -d "dataset/val" ]; then
    echo "ERROR: Validation dataset not found at dataset/val"
    echo "Please ensure your dataset is properly structured."
    echo ""
    exit 1
fi

echo "Starting quick test training..."
echo ""

# Run the quick test with SDCNet2DMaskStandalone
python quick_test_training.py --model SDCNet2DMaskStandalone

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Quick test PASSED!"
    echo "Your SDCNet2D setup is working correctly."
    echo "You can now proceed with full training using:"
    echo "  python main_mask.py --model SDCNet2DMaskStandalone"
else
    echo ""
    echo "❌ Quick test FAILED!"
    echo "Check the error messages above for details."
    exit 1
fi
