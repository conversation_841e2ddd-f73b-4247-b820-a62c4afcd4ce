#!/usr/bin/env python3
"""
Training script with fixed warping and reduced disk usage.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import argparse
import os
import sys
from datetime import datetime
from tqdm import tqdm
import gc

# Add models directory to path
sys.path.append('models')
sys.path.append('datasets')

def create_args():
    """Create arguments for model initialization."""
    args = argparse.Namespace()
    args.rgb_max = 255.0
    args.sequence_length = 2
    args.batch_size = 2  # Reduced batch size to save memory
    args.lr = 1e-4
    args.epochs = 20  # Reduced epochs for testing
    args.save_freq = 5  # Save less frequently
    args.print_freq = 50  # Print less frequently
    args.device = 'cuda' if torch.cuda.is_available() else 'cpu'
    return args

def create_model_and_optimizer(args):
    """Create model and optimizer."""
    from sdc_net2d_mask_standalone import SDCNet2DMaskStandalone
    
    model = SDCNet2DMaskStandalone(args)
    model = model.to(args.device)
    
    optimizer = optim.Adam(model.parameters(), lr=args.lr)
    
    return model, optimizer

def create_data_loaders(args):
    """Create data loaders."""
    from mask_frame_loader import MaskFrameLoader
    
    train_dataset = MaskFrameLoader(args, True, "dataset/train")
    val_dataset = MaskFrameLoader(args, False, "dataset/val")
    
    train_loader = DataLoader(
        train_dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        num_workers=2,  # Reduced workers
        pin_memory=False  # Disabled to save memory
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=args.batch_size, 
        shuffle=False, 
        num_workers=2,
        pin_memory=False
    )
    
    return train_loader, val_loader

def train_epoch(model, train_loader, optimizer, args, epoch):
    """Train for one epoch."""
    model.train()
    total_loss = 0.0
    total_color_loss = 0.0
    num_batches = 0
    
    pbar = tqdm(train_loader, desc=f"Epoch {epoch}")
    
    for batch_idx, batch in enumerate(pbar):
        # Move to device
        inputs = {}
        for k in batch:
            if k in ["image", "mask"]:
                inputs[k] = [b.to(args.device) for b in batch[k]]
            else:
                inputs[k] = batch[k]
        
        # Forward pass
        optimizer.zero_grad()
        
        try:
            losses, pred_image, target_image = model(inputs)
            loss = losses['tot']
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            # Update statistics
            total_loss += loss.item()
            total_color_loss += losses['color'].item()
            num_batches += 1
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'Color': f"{losses['color'].item():.6f}",
                'GPU': f"{torch.cuda.memory_allocated()/1024**3:.1f}GB" if torch.cuda.is_available() else "CPU"
            })
            
            # Print progress
            if batch_idx % args.print_freq == 0:
                print(f"  Batch {batch_idx}: Loss={loss.item():.4f}, Color={losses['color'].item():.6f}")
            
            # Clean up memory
            del losses, pred_image, target_image, loss
            if batch_idx % 10 == 0:
                torch.cuda.empty_cache()
                
        except Exception as e:
            print(f"Error in batch {batch_idx}: {e}")
            continue
    
    avg_loss = total_loss / max(num_batches, 1)
    avg_color_loss = total_color_loss / max(num_batches, 1)
    
    return avg_loss, avg_color_loss

def validate_epoch(model, val_loader, args, epoch):
    """Validate for one epoch."""
    model.eval()
    total_loss = 0.0
    total_color_loss = 0.0
    num_batches = 0
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc=f"Validation {epoch}")
        
        for batch_idx, batch in enumerate(pbar):
            # Move to device
            inputs = {}
            for k in batch:
                if k in ["image", "mask"]:
                    inputs[k] = [b.to(args.device) for b in batch[k]]
                else:
                    inputs[k] = batch[k]
            
            try:
                losses, pred_image, target_image = model(inputs)
                loss = losses['tot']
                
                total_loss += loss.item()
                total_color_loss += losses['color'].item()
                num_batches += 1
                
                pbar.set_postfix({
                    'Loss': f"{loss.item():.4f}",
                    'Color': f"{losses['color'].item():.6f}"
                })
                
                # Clean up memory
                del losses, pred_image, target_image, loss
                
            except Exception as e:
                print(f"Error in validation batch {batch_idx}: {e}")
                continue
    
    avg_loss = total_loss / max(num_batches, 1)
    avg_color_loss = total_color_loss / max(num_batches, 1)
    
    return avg_loss, avg_color_loss

def save_checkpoint(model, optimizer, epoch, loss, save_dir):
    """Save checkpoint with minimal disk usage."""
    os.makedirs(save_dir, exist_ok=True)
    
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss,
    }
    
    # Save only the latest checkpoint to save space
    checkpoint_path = os.path.join(save_dir, 'latest_checkpoint.pth')
    torch.save(checkpoint, checkpoint_path)
    
    print(f"Checkpoint saved: {checkpoint_path}")

def main():
    """Main training function."""
    print("=" * 60)
    print("Training SDCNet with Fixed Warping")
    print("=" * 60)
    
    # Create arguments
    args = create_args()
    print(f"Device: {args.device}")
    print(f"Batch size: {args.batch_size}")
    print(f"Learning rate: {args.lr}")
    print(f"Epochs: {args.epochs}")
    
    # Create save directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = f"training_fixed_warping_{timestamp}"
    os.makedirs(save_dir, exist_ok=True)
    print(f"Save directory: {save_dir}")
    
    # Create model and optimizer
    print("\nCreating model and optimizer...")
    model, optimizer = create_model_and_optimizer(args)
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Create data loaders
    print("\nCreating data loaders...")
    train_loader, val_loader = create_data_loaders(args)
    print(f"Training samples: {len(train_loader.dataset)}")
    print(f"Validation samples: {len(val_loader.dataset)}")
    
    # Training loop
    print("\nStarting training...")
    best_val_loss = float('inf')
    
    for epoch in range(args.epochs):
        print(f"\n{'='*60}")
        print(f"Epoch {epoch+1}/{args.epochs}")
        print(f"{'='*60}")
        
        # Train
        train_loss, train_color_loss = train_epoch(model, train_loader, optimizer, args, epoch)
        print(f"Training - Loss: {train_loss:.6f}, Color: {train_color_loss:.6f}")
        
        # Validate
        val_loss, val_color_loss = validate_epoch(model, val_loader, args, epoch)
        print(f"Validation - Loss: {val_loss:.6f}, Color: {val_color_loss:.6f}")
        
        # Save checkpoint if improved
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            save_checkpoint(model, optimizer, epoch, val_loss, save_dir)
            print(f"✓ New best validation loss: {val_loss:.6f}")
        
        # Save periodic checkpoint
        if (epoch + 1) % args.save_freq == 0:
            save_checkpoint(model, optimizer, epoch, val_loss, save_dir)
        
        # Clean up memory
        torch.cuda.empty_cache()
        gc.collect()
    
    print(f"\n{'='*60}")
    print("Training completed!")
    print(f"Best validation loss: {best_val_loss:.6f}")
    print(f"Model saved in: {save_dir}")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
