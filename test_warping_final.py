#!/usr/bin/env python3
"""
Final test of warping implementation to verify it's working correctly.
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import os
import sys

# Add models directory to path
sys.path.append('models')

def test_warping_implementation():
    """Test the warping implementation thoroughly."""
    print("Testing warping implementation...")

    try:
        from sdc_net2d_mask_standalone import NativeResample2d

        # Create warper
        warper = NativeResample2d(bilinear=False)  # Use nearest neighbor for exact testing

        # Create a simple test pattern
        H, W = 32, 32
        img = torch.zeros(1, 1, H, W)

        # Create a distinctive pattern
        img[0, 0, 10:15, 10:15] = 1.0  # White square
        img[0, 0, 12, 12] = 0.5  # Gray center pixel

        print("Original pattern created")

        # Test 1: Zero flow (identity)
        print("\nTest 1: Zero flow (should be identity)")
        zero_flow = torch.zeros(1, 2, H, W)
        result_zero = warper(img, zero_flow)

        diff_zero = torch.abs(result_zero - img).max().item()
        print(f"Max difference with zero flow: {diff_zero:.8f}")

        if diff_zero < 1e-6:
            print("✓ Zero flow test PASSED")
            zero_passed = True
        else:
            print("✗ Zero flow test FAILED")
            zero_passed = False

        # Test 2: Simple translations
        print("\nTest 2: Translation tests")
        translations = [(3, 0), (0, 3), (2, 2), (-1, 1)]
        translation_passed = True

        for dx, dy in translations:
            print(f"  Testing translation: dx={dx}, dy={dy}")

            # Create flow field
            flow = torch.zeros(1, 2, H, W)
            flow[0, 0, :, :] = dx  # x displacement
            flow[0, 1, :, :] = dy  # y displacement

            # Apply warping
            result = warper(img, flow)

            # Find the white square in original and result
            orig_mask = (img[0, 0] > 0.9)
            result_mask = (result[0, 0] > 0.9)

            if torch.any(orig_mask) and torch.any(result_mask):
                # Calculate centers
                orig_coords = torch.where(orig_mask)
                result_coords = torch.where(result_mask)

                orig_center_y = orig_coords[0].float().mean()
                orig_center_x = orig_coords[1].float().mean()
                result_center_y = result_coords[0].float().mean()
                result_center_x = result_coords[1].float().mean()

                actual_dx = result_center_x - orig_center_x
                actual_dy = result_center_y - orig_center_y

                error_x = abs(actual_dx - dx)
                error_y = abs(actual_dy - dy)

                print(f"    Expected: ({dx}, {dy})")
                print(f"    Actual: ({actual_dx:.1f}, {actual_dy:.1f})")
                print(f"    Error: ({error_x:.1f}, {error_y:.1f})")

                if error_x < 0.5 and error_y < 0.5:
                    print(f"    ✓ Translation test PASSED")
                else:
                    print(f"    ✗ Translation test FAILED")
                    translation_passed = False
            else:
                print(f"    ? Could not find pattern in result")
                translation_passed = False

        # Test 3: Consistency check
        print("\nTest 3: Consistency check")
        # Apply flow twice in opposite directions should give identity
        flow_right = torch.zeros(1, 2, H, W)
        flow_right[0, 0, :, :] = 5  # Move right

        flow_left = torch.zeros(1, 2, H, W)
        flow_left[0, 0, :, :] = -5  # Move left

        result_right = warper(img, flow_right)
        result_back = warper(result_right, flow_left)

        consistency_diff = torch.abs(result_back - img).max().item()
        print(f"Consistency difference: {consistency_diff:.6f}")

        if consistency_diff < 0.1:  # Allow some interpolation error
            print("✓ Consistency test PASSED")
            consistency_passed = True
        else:
            print("✗ Consistency test FAILED")
            consistency_passed = False

        # Overall result
        print(f"\n{'='*50}")
        print("WARPING TEST RESULTS:")
        print(f"Zero flow test: {'PASS' if zero_passed else 'FAIL'}")
        print(f"Translation tests: {'PASS' if translation_passed else 'FAIL'}")
        print(f"Consistency test: {'PASS' if consistency_passed else 'FAIL'}")

        overall_passed = zero_passed and translation_passed and consistency_passed
        print(f"Overall: {'PASS' if overall_passed else 'FAIL'}")
        print(f"{'='*50}")

        return overall_passed

    except Exception as e:
        print(f"Error during warping test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """Test that the model can be created and run."""
    print("\nTesting model creation...")

    try:
        from sdc_net2d_mask_standalone import SDCNet2DMaskStandalone
        import argparse

        # Create args
        args = argparse.Namespace()
        args.rgb_max = 255.0
        args.sequence_length = 2

        # Create model
        model = SDCNet2DMaskStandalone(args)
        model.eval()

        print("✓ Model created successfully")

        # Test forward pass with dummy data
        batch_size = 1
        H, W = 128, 160

        # Create dummy input
        images = [torch.randn(batch_size, 3, H*2, W*2) for _ in range(3)]
        masks = [torch.ones(batch_size, 1, H*2, W*2) for _ in range(3)]

        input_dict = {
            'image': images,
            'mask': masks
        }

        with torch.no_grad():
            result = model(input_dict)
            if len(result) == 5:
                losses, pred_image, target_image, pred_mask, target_mask = result
            else:
                losses, pred_image, target_image = result

        print("✓ Forward pass successful")
        print(f"  Color loss: {losses['color']:.6f}")
        print(f"  Total loss: {losses['tot']:.6f}")
        print(f"  Prediction shape: {pred_image.shape}")

        return True

    except Exception as e:
        print(f"✗ Model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("Final Warping and Model Test")
    print("=" * 50)

    # Test warping
    warping_passed = test_warping_implementation()

    # Test model
    model_passed = test_model_creation()

    print(f"\n{'='*50}")
    print("FINAL TEST RESULTS:")
    print(f"Warping implementation: {'PASS' if warping_passed else 'FAIL'}")
    print(f"Model creation: {'PASS' if model_passed else 'FAIL'}")

    overall_passed = warping_passed and model_passed
    print(f"Overall: {'PASS' if overall_passed else 'FAIL'}")

    if overall_passed:
        print("\n✓ All tests passed! Ready for training with fixed warping.")
    else:
        print("\n✗ Some tests failed. Please check the implementation.")

    print(f"{'='*50}")

if __name__ == "__main__":
    main()
