#!/usr/bin/env python3
"""
Batch test for different offset corrections.
Run this manually from PowerShell with: python test_offset_batch.py
"""

import sys
import os
sys.path.append('models')

def test_offset_values():
    """Test different offset values to find the best correction."""
    try:
        import torch
        import numpy as np
        from PIL import Image
        
        print("Testing different offset corrections...")
        print("=" * 60)
        
        # Test different offset combinations
        offset_combinations = [
            (0.0, 0.0),    # No correction
            (-0.5, -0.5),  # Small correction
            (-1.0, -1.0),  # Medium correction
            (-1.5, -1.5),  # Large correction
            (-2.0, -2.0),  # Very large correction
            (-1.0, -0.5),  # Asymmetric correction
            (-0.5, -1.0),  # Asymmetric correction
        ]
        
        # Create test pattern
        H, W = 128, 160
        img = torch.zeros(1, 3, H, W)
        
        # Add distinctive pattern
        y_center, x_center = H//2, W//2
        size = 16
        img[0, :, y_center-size//2:y_center+size//2, x_center-size//2:x_center+size//2] = 1.0
        
        # Add center marker
        img[0, 0, y_center-1:y_center+1, x_center-1:x_center+1] = 1.0  # Red center
        
        print(f"Test pattern center: ({y_center}, {x_center})")
        print()
        
        # Test each offset combination
        for i, (offset_x, offset_y) in enumerate(offset_combinations):
            print(f"Test {i+1}: offset_x={offset_x}, offset_y={offset_y}")
            
            # Temporarily modify the offset values in the warping function
            # We'll create a custom warper for this test
            result = test_warping_with_offset(img, offset_x, offset_y)
            
            if result is not None:
                # Find pattern center in result
                red_channel = result[0, 0]
                if red_channel.max() > 0.5:
                    coords = torch.where(red_channel > 0.9)
                    if len(coords[0]) > 0:
                        result_y = coords[0].float().mean().item()
                        result_x = coords[1].float().mean().item()
                        
                        offset_detected_y = result_y - y_center
                        offset_detected_x = result_x - x_center
                        
                        print(f"  Result center: ({result_x:.1f}, {result_y:.1f})")
                        print(f"  Detected offset: ({offset_detected_x:.1f}, {offset_detected_y:.1f})")
                        
                        # Calculate total offset magnitude
                        total_offset = np.sqrt(offset_detected_x**2 + offset_detected_y**2)
                        print(f"  Total offset magnitude: {total_offset:.2f} pixels")
                        
                        if total_offset < 0.5:
                            print("  ✓ EXCELLENT - Very small offset")
                        elif total_offset < 1.0:
                            print("  ✓ GOOD - Small offset")
                        elif total_offset < 2.0:
                            print("  ~ OK - Moderate offset")
                        else:
                            print("  ✗ POOR - Large offset")
                    else:
                        print("  ? Pattern not found in result")
                else:
                    print("  ? No bright pixels found in result")
            else:
                print("  ✗ Warping failed")
            
            print()
        
        print("=" * 60)
        print("Manual testing instructions:")
        print("1. Look at the results above")
        print("2. Find the offset combination with the smallest total offset")
        print("3. Update the offset values in the warping functions")
        print("4. Test with real images")
        
    except Exception as e:
        print(f"Error during batch test: {e}")
        import traceback
        traceback.print_exc()

def test_warping_with_offset(img, offset_x, offset_y):
    """Test warping with specific offset values."""
    try:
        import torch
        import torch.nn.functional as F
        
        B, C, H, W = img.shape
        
        # Create coordinate grids
        grid_y, grid_x = torch.meshgrid(
            torch.arange(H, dtype=torch.float32),
            torch.arange(W, dtype=torch.float32),
            indexing='ij'
        )
        
        # Expand grids to batch size
        grid_x = grid_x.unsqueeze(0).expand(B, -1, -1)
        grid_y = grid_y.unsqueeze(0).expand(B, -1, -1)
        
        # Zero flow (identity test)
        flow = torch.zeros(B, 2, H, W)
        
        # Apply flow
        new_x = grid_x + flow[:, 0]
        new_y = grid_y + flow[:, 1]
        
        # Apply offset correction
        new_x = new_x + offset_x
        new_y = new_y + offset_y
        
        # Normalize coordinates
        norm_x = 2.0 * new_x / (W - 1) - 1.0
        norm_y = 2.0 * new_y / (H - 1) - 1.0
        
        # Create grid for grid_sample
        grid = torch.stack([norm_x, norm_y], dim=-1)
        
        # Apply warping
        result = F.grid_sample(
            img, grid,
            mode='bilinear',
            padding_mode='border',
            align_corners=True
        )
        
        return result
        
    except Exception as e:
        print(f"  Error in warping: {e}")
        return None

if __name__ == "__main__":
    print("Batch Offset Correction Test")
    print("=" * 60)
    test_offset_values()
