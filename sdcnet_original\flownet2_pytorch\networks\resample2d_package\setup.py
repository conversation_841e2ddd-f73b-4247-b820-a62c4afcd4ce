# #!/usr/bin/env python3
# import os
# import torch
#
# from setuptools import setup
# from torch.utils.cpp_extension import BuildExtension, CUDAExtension
#
# cxx_args = ['-std=c++11']
#
# nvcc_args = [
#     '-gencode', 'arch=compute_50,code=sm_50',
#     '-gencode', 'arch=compute_52,code=sm_52',
#     '-gencode', 'arch=compute_60,code=sm_60',
#     '-gencode', 'arch=compute_61,code=sm_61',
#     '-gencode', 'arch=compute_70,code=sm_70',
#     '-gencode', 'arch=compute_70,code=compute_70'
# ]
#
# setup(
#     name='resample2d_cuda',
#     ext_modules=[
#         CUDAExtension('resample2d_cuda', [
#             'resample2d_cuda.cc',
#             'resample2d_kernel.cu'
#         ], extra_compile_args={'cxx': cxx_args, 'nvcc': nvcc_args})
#     ],
#     cmdclass={
#         'build_ext': BuildExtension
#     })






## cuda 10 compatiblity attempt from https://github.com/MichiganCOG/flownet2-pytorch-1.0.1-with-CUDA-10/blob/master/src/networks/correlation_package/setup.py
#!/usr/bin/env python3
import os
import torch

from setuptools import setup
from torch.utils.cpp_extension import BuildExtension, CUDAExtension

cxx_args = ['-std=c++14']

nvcc_args = [
    '-gencode', 'arch=compute_50,code=sm_50',
    '-gencode', 'arch=compute_52,code=sm_52',
    '-gencode', 'arch=compute_60,code=sm_60',
    '-gencode', 'arch=compute_61,code=sm_61',
    '-gencode', 'arch=compute_70,code=sm_70',
    '-gencode', 'arch=compute_70,code=compute_70',
    '-gencode', 'arch=compute_75,code=sm_75',
    '-gencode', 'arch=compute_75,code=compute_75'
]

extra_compile_args={
    'cxx': [],
    'nvcc': [
        '-allow-unsupported-compiler',
        '-std=c++14',
        # altri flag...
    ]
}


setup(
    name='resample2d_cuda',
    ext_modules=[
        CUDAExtension('resample2d_cuda', [
            'resample2d_cuda.cc',
            'resample2d_kernel.cu'
        ], extra_compile_args={'cxx': cxx_args, 'nvcc': nvcc_args})
    ],
    cmdclass={
        'build_ext': BuildExtension
    })
