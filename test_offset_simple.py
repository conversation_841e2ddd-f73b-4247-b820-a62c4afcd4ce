#!/usr/bin/env python3
"""
Simple test for offset correction.
"""

import sys
import os
sys.path.append('models')

def test_offset_correction():
    """Test the offset correction with a simple pattern."""
    try:
        import torch
        import torch.nn.functional as F
        from PIL import Image
        import numpy as np
        
        print("Testing offset correction...")
        
        # Create a simple test pattern
        H, W = 128, 160
        
        # Create input image with a distinctive pattern
        img = torch.zeros(1, 3, H, W)
        
        # Add a bright square pattern
        y_center, x_center = H//2, W//2
        size = 20
        img[0, :, y_center-size//2:y_center+size//2, x_center-size//2:x_center+size//2] = 1.0
        
        # Add a smaller red dot in the center for precise tracking
        img[0, 0, y_center-2:y_center+2, x_center-2:x_center+2] = 1.0  # Red
        img[0, 1, y_center-2:y_center+2, x_center-2:x_center+2] = 0.0  # Green
        img[0, 2, y_center-2:y_center+2, x_center-2:x_center+2] = 0.0  # Blue
        
        print(f"Created test pattern with center at ({y_center}, {x_center})")
        
        # Test with zero flow (should be identity)
        flow = torch.zeros(1, 2, H, W)
        
        # Import our warping function
        from sdc_net2d_mask_original import NativeResample2d
        warper = NativeResample2d()
        
        result = warper(img, flow)
        
        # Calculate difference
        diff = torch.abs(result - img).max().item()
        print(f"Max difference with zero flow: {diff:.8f}")
        
        # Find pattern centers
        def find_pattern_center(tensor):
            # Find the red channel peak
            red_channel = tensor[0, 0]  # [H, W]
            max_val = red_channel.max()
            if max_val > 0.5:
                coords = torch.where(red_channel == max_val)
                center_y = coords[0].float().mean().item()
                center_x = coords[1].float().mean().item()
                return center_y, center_x
            return None, None
        
        orig_y, orig_x = find_pattern_center(img)
        result_y, result_x = find_pattern_center(result)
        
        if orig_y is not None and result_y is not None:
            offset_y = result_y - orig_y
            offset_x = result_x - orig_x
            print(f"Pattern offset: Y={offset_y:.2f}, X={offset_x:.2f}")
            
            if abs(offset_y) < 0.1 and abs(offset_x) < 0.1:
                print("✓ Zero flow test PASSED - no significant offset")
                return True
            else:
                print(f"✗ Zero flow test FAILED - offset detected: ({offset_x:.2f}, {offset_y:.2f})")
                return False
        else:
            print("? Could not detect pattern in result")
            return False
            
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Simple Offset Test")
    print("=" * 40)
    
    success = test_offset_correction()
    
    print("=" * 40)
    if success:
        print("✓ Test PASSED")
    else:
        print("✗ Test FAILED")
