#!/usr/bin/env python
"""
Simple Video Generation Script for SDCNet2D Results

This script uses a more reliable FFmpeg approach for creating videos from frame sequences.

Usage:
    python generate_videos_simple.py --input_dir predicted_2025_05_27_16_39_24
"""

import os
import sys
import argparse
import subprocess
import glob
from datetime import datetime
from tqdm import tqdm

def create_args():
    """Create argument parser for video generation."""
    parser = argparse.ArgumentParser(description='Generate Videos from SDCNet2D Predictions - Simple Version')
    
    # Input/Output paths
    parser.add_argument('--input_dir', required=True, help='Input directory from inference_test.py')
    parser.add_argument('--output_dir', default=None, help='Output directory for videos')
    
    # Video parameters
    parser.add_argument('--fps', type=int, default=5, help='Frames per second for output videos')
    parser.add_argument('--quality', type=str, default='medium', choices=['low', 'medium', 'high'], 
                       help='Video quality preset')
    
    # Comparison options
    parser.add_argument('--create_comparison', action='store_true', 
                       help='Create side-by-side comparison videos')
    parser.add_argument('--create_individual', action='store_true', default=True,
                       help='Create individual videos for predicted and ground truth')
    
    # FFmpeg options
    parser.add_argument('--ffmpeg_path', default='ffmpeg', help='Path to FFmpeg executable')
    parser.add_argument('--overwrite', action='store_true', help='Overwrite existing videos')
    
    return parser.parse_args()

def check_ffmpeg(ffmpeg_path):
    """Check if FFmpeg is available."""
    try:
        result = subprocess.run([ffmpeg_path, '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ FFmpeg found: {ffmpeg_path}")
            return True
        else:
            print(f"❌ FFmpeg not working: {ffmpeg_path}")
            return False
    except Exception as e:
        print(f"❌ FFmpeg not found: {e}")
        return False

def get_quality_settings(quality):
    """Get FFmpeg quality settings."""
    settings = {
        'low': ['-crf', '28'],
        'medium': ['-crf', '23'],
        'high': ['-crf', '18']
    }
    return settings.get(quality, settings['medium'])

def create_video_simple(frame_dir, output_path, fps, quality_settings, ffmpeg_path, overwrite=False):
    """Create video using simple glob pattern method."""
    if not os.path.exists(frame_dir):
        print(f"❌ Frame directory not found: {frame_dir}")
        return False
    
    # Get frame files
    jpg_files = glob.glob(os.path.join(frame_dir, "*.jpg"))
    png_files = glob.glob(os.path.join(frame_dir, "*.png"))
    frame_files = jpg_files + png_files
    
    if not frame_files:
        print(f"❌ No frame files found in: {frame_dir}")
        return False
    
    # Sort frames
    frame_files.sort()
    
    print(f"  Found {len(frame_files)} frames")
    print(f"  Expected duration: {len(frame_files)/fps:.1f} seconds at {fps} FPS")
    
    # Check if output exists
    if os.path.exists(output_path) and not overwrite:
        print(f"  ⚠️  Video already exists: {output_path}")
        return True
    
    # Method 1: Try glob pattern (most reliable)
    frame_pattern = os.path.join(frame_dir, "*.jpg")
    if not jpg_files:
        frame_pattern = os.path.join(frame_dir, "*.png")
    
    cmd = [
        ffmpeg_path,
        '-framerate', str(fps),
        '-pattern_type', 'glob',
        '-i', frame_pattern,
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p'
    ] + quality_settings + [
        '-y' if overwrite else '-n',
        output_path
    ]
    
    print(f"  Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"  ✓ Video created: {output_path}")
            return True
        else:
            print(f"  ❌ FFmpeg error: {result.stderr}")
            
            # Method 2: Fallback - create file list manually
            print(f"  Trying fallback method...")
            return create_video_fallback(frame_files, output_path, fps, quality_settings, ffmpeg_path, overwrite)
            
    except Exception as e:
        print(f"  ❌ Error running FFmpeg: {e}")
        return False

def create_video_fallback(frame_files, output_path, fps, quality_settings, ffmpeg_path, overwrite=False):
    """Fallback method using explicit file list."""
    temp_list_path = output_path + "_temp_list.txt"
    
    try:
        # Create file list with duration for each frame
        with open(temp_list_path, 'w') as f:
            for frame_file in frame_files:
                f.write(f"file '{os.path.abspath(frame_file)}'\n")
                f.write(f"duration {1.0/fps}\n")
            # Add last frame again for proper ending
            if frame_files:
                f.write(f"file '{os.path.abspath(frame_files[-1])}'\n")
        
        cmd = [
            ffmpeg_path,
            '-f', 'concat',
            '-safe', '0',
            '-i', temp_list_path,
            '-vsync', 'vfr',
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p'
        ] + quality_settings + [
            '-y' if overwrite else '-n',
            output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"  ✓ Video created (fallback): {output_path}")
            return True
        else:
            print(f"  ❌ Fallback failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ Fallback error: {e}")
        return False
    finally:
        if os.path.exists(temp_list_path):
            os.remove(temp_list_path)

def create_comparison_video_simple(pred_dir, gt_dir, output_path, fps, quality_settings, ffmpeg_path, overwrite=False):
    """Create side-by-side comparison video using simple method."""
    if not os.path.exists(pred_dir) or not os.path.exists(gt_dir):
        print(f"❌ Missing directories for comparison")
        return False
    
    # Check if output exists
    if os.path.exists(output_path) and not overwrite:
        print(f"  ⚠️  Comparison video already exists: {output_path}")
        return True
    
    # Use glob patterns for both inputs
    pred_pattern = os.path.join(pred_dir, "*.jpg")
    gt_pattern = os.path.join(gt_dir, "*.jpg")
    
    # Check if we have PNG instead
    if not glob.glob(pred_pattern):
        pred_pattern = os.path.join(pred_dir, "*.png")
    if not glob.glob(gt_pattern):
        gt_pattern = os.path.join(gt_dir, "*.png")
    
    cmd = [
        ffmpeg_path,
        '-framerate', str(fps),
        '-pattern_type', 'glob', '-i', pred_pattern,
        '-framerate', str(fps),
        '-pattern_type', 'glob', '-i', gt_pattern,
        '-filter_complex', '[0:v][1:v]hstack=inputs=2[v]',
        '-map', '[v]',
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p'
    ] + quality_settings + [
        '-y' if overwrite else '-n',
        output_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"  ✓ Comparison video created: {output_path}")
            return True
        else:
            print(f"  ❌ Comparison failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ Comparison error: {e}")
        return False

def generate_videos(args):
    """Generate videos from prediction frames."""
    # Check input directory
    if not os.path.exists(args.input_dir):
        print(f"❌ Input directory not found: {args.input_dir}")
        return False
    
    # Create output directory
    if args.output_dir is None:
        timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        args.output_dir = f"videos_{timestamp}"
    
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"Output directory: {args.output_dir}")
    
    # Get quality settings
    quality_settings = get_quality_settings(args.quality)
    
    # Find video directories
    pred_base_dir = os.path.join(args.input_dir, "predicted")
    gt_base_dir = os.path.join(args.input_dir, "ground_truth")
    
    if not os.path.exists(pred_base_dir):
        print(f"❌ Predicted frames directory not found: {pred_base_dir}")
        return False
    
    if not os.path.exists(gt_base_dir):
        print(f"❌ Ground truth frames directory not found: {gt_base_dir}")
        return False
    
    # Get video names
    video_names = [d for d in os.listdir(pred_base_dir) 
                   if os.path.isdir(os.path.join(pred_base_dir, d))]
    
    if not video_names:
        print(f"❌ No video directories found in: {pred_base_dir}")
        return False
    
    print(f"Found {len(video_names)} videos to process")
    
    # Process each video
    success_count = 0
    
    for video_name in tqdm(video_names, desc="Generating videos"):
        print(f"\nProcessing video: {video_name}")
        
        pred_video_dir = os.path.join(pred_base_dir, video_name)
        gt_video_dir = os.path.join(gt_base_dir, video_name)
        
        video_success = True
        
        # Create individual videos
        if args.create_individual:
            # Predicted video
            pred_output = os.path.join(args.output_dir, f"{video_name}_predicted.mp4")
            if not create_video_simple(pred_video_dir, pred_output, args.fps, 
                                     quality_settings, args.ffmpeg_path, args.overwrite):
                video_success = False
            
            # Ground truth video
            gt_output = os.path.join(args.output_dir, f"{video_name}_ground_truth.mp4")
            if not create_video_simple(gt_video_dir, gt_output, args.fps, 
                                     quality_settings, args.ffmpeg_path, args.overwrite):
                video_success = False
        
        # Create comparison video
        if args.create_comparison:
            comp_output = os.path.join(args.output_dir, f"{video_name}_comparison.mp4")
            if not create_comparison_video_simple(pred_video_dir, gt_video_dir, comp_output, args.fps,
                                                quality_settings, args.ffmpeg_path, args.overwrite):
                video_success = False
        
        if video_success:
            success_count += 1
    
    # Summary
    print(f"\n🎉 Video generation completed!")
    print(f"Successfully processed: {success_count}/{len(video_names)} videos")
    print(f"Output directory: {args.output_dir}")
    
    return success_count == len(video_names)

def main():
    """Main function."""
    args = create_args()
    
    print("=" * 60)
    print("SDCNet2D Video Generation - Simple Version")
    print("=" * 60)
    print(f"Input directory: {args.input_dir}")
    print(f"FPS: {args.fps}")
    print(f"Quality: {args.quality}")
    print("=" * 60)
    
    # Check FFmpeg
    if not check_ffmpeg(args.ffmpeg_path):
        print("❌ FFmpeg is required but not found. Please install FFmpeg.")
        return False
    
    try:
        success = generate_videos(args)
        if success:
            print(f"\n✅ All videos generated successfully!")
        else:
            print(f"\n⚠️  Some videos failed to generate. Check the logs above.")
        return success
        
    except Exception as e:
        print(f"❌ Video generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
