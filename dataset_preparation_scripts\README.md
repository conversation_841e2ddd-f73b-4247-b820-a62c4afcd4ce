# Dataset Preparation Scripts

This directory contains utility scripts to help prepare your dataset for SDCNet2D with mask support.

## 📁 Scripts Overview

### 1. `extract_frames.py`
Extracts frames from video files and organizes them for the dataset.

**Usage:**
```bash
python extract_frames.py --input_video video.mp4 --output_dir dataset/train/X/video001 --fps 30
```

**Parameters:**
- `--input_video`: Path to input video file
- `--output_dir`: Directory to save extracted frames
- `--fps`: Target FPS (optional, uses original if not specified)
- `--start_frame`: Frame to start extraction from (default: 0)
- `--max_frames`: Maximum number of frames to extract (optional)

### 2. `validate_dataset.py`
Validates the dataset structure and reports any issues.

**Usage:**
```bash
python validate_dataset.py --dataset_root ./dataset
```

**Features:**
- Checks directory structure
- Validates file correspondence between frames and masks
- Verifies mask format (binary 0/255 values)
- Reports statistics and issues
- Generates comprehensive dataset report

## 🚀 Quick Start Workflow

### Step 1: Extract Frames
```bash
# Extract frames from your videos
python extract_frames.py --input_video raw_videos/video1.mp4 --output_dir dataset/train/X/video001 --fps 30
python extract_frames.py --input_video raw_videos/video2.mp4 --output_dir dataset/train/X/video002 --fps 30
```

### Step 2: Generate Masks
Use your preferred segmentation method to generate binary masks:
- Manual annotation tools (e.g., CVAT, LabelMe)
- Automatic segmentation (e.g., SAM, Detectron2)
- Custom segmentation pipeline

Save masks to corresponding `_binary_ground_truth` directories:
```
dataset/train/X/video001_binary_ground_truth/
dataset/train/X/video002_binary_ground_truth/
```

### Step 3: Organize Target Frames
Copy or link target frames to Y directories:
```bash
cp -r dataset/train/X/video001/* dataset/train/Y/video001/
cp -r dataset/train/X/video002/* dataset/train/Y/video002/
```

### Step 4: Validate Dataset
```bash
python validate_dataset.py --dataset_root ./dataset
```

## 📋 Requirements

Install required dependencies:
```bash
pip install opencv-python numpy natsort
```

## 🔧 Customization

### Batch Processing
Create a batch script for multiple videos:

```bash
#!/bin/bash
# batch_extract.sh

for video in raw_videos/*.mp4; do
    basename=$(basename "$video" .mp4)
    echo "Processing $basename..."
    python extract_frames.py --input_video "$video" --output_dir "dataset/train/X/$basename" --fps 30
done
```

### Custom Validation
Modify `validate_dataset.py` to add custom validation rules:
- Specific resolution requirements
- Frame rate consistency checks
- Custom mask validation criteria

## 🐛 Troubleshooting

### Common Issues

#### "Cannot open video"
- Check video file format and codec
- Ensure OpenCV supports the video format
- Try converting video to MP4 with H.264 codec

#### "Mask validation failed"
- Ensure masks are grayscale images
- Check that mask values are exactly 0 and 255
- Verify mask dimensions match frame dimensions

#### "File count mismatch"
- Ensure identical filenames between frames and masks
- Check for hidden files or system files
- Verify all files have supported extensions

### Debug Commands

```bash
# Check video properties
ffprobe -v quiet -print_format json -show_format -show_streams input_video.mp4

# Verify mask values
python -c "
import cv2
import numpy as np
mask = cv2.imread('path/to/mask.png', 0)
print('Unique values:', np.unique(mask))
print('Shape:', mask.shape)
"

# Count files in directory
find dataset/train/X/video001/ -name "*.png" | wc -l
```

## 📈 Best Practices

### Data Quality
- Use consistent frame rates across videos
- Maintain high image quality
- Ensure accurate mask boundaries
- Check temporal consistency

### Organization
- Use descriptive video names
- Follow consistent naming patterns
- Keep original data as backup
- Document data sources and processing steps

### Validation
- Run validation after each processing step
- Check sample images manually
- Verify mask quality visually
- Test with small dataset first

This toolkit ensures your dataset is properly formatted and ready for SDCNet2D training with mask support.
