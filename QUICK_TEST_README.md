# SDCNet2D Quick Test Training

## Overview

The quick test training is designed to verify that your SDCNet2D setup is working correctly before running a full training session. It performs a minimal 2-epoch training run to test all components of the pipeline.

## What it Tests

✅ **Model Loading**: Verifies the model can be instantiated correctly  
✅ **Dataset Loading**: Checks that your dataset structure is correct  
✅ **Training Loop**: Ensures forward/backward passes work without errors  
✅ **Loss Computation**: Validates that loss values are stable and not NaN  
✅ **Checkpointing**: Tests model saving and loading functionality  

## Prerequisites

1. **Environment**: Activate your conda environment
   ```bash
   conda activate sdcnet_env
   ```

2. **Dataset Structure**: Ensure your dataset follows this structure:
   ```
   dataset/
   ├── train/
   │   ├── X/  (frame images)
   │   └── Y/  (mask images)
   └── val/
       ├── X/  (frame images)
       └── Y/  (mask images)
   ```

3. **FlowNet2 Checkpoint**: Ensure you have the FlowNet2 checkpoint at:
   ```
   pretrained_models/FlowNet2_checkpoint.pth.tar
   ```

## How to Run

### Option 1: Using Batch Script (Windows)
```cmd
quick_test.bat
```

### Option 2: Using Shell Script (Linux/Mac)
```bash
./quick_test.sh
```

### Option 3: Direct Python Command
```bash
python quick_test_training.py --model SDCNet2DMaskStandalone
```

## Quick Test Parameters

The quick test uses these optimized parameters for fast execution:

- **Epochs**: 2 (instead of hundreds)
- **Batch Size**: 2 (small for quick processing)
- **Training Batches**: 3 per epoch (limited subset)
- **Validation Batches**: 2 per epoch (limited subset)
- **Image Size**: 128x128 (smaller for speed)
- **Workers**: 2 (reduced for stability)

## Expected Output

A successful quick test will show:

```
SDCNet2D Quick Test Training
==================================================
=== Checking Dataset Structure ===
✓ Training dataset found: dataset/train
✓ Validation dataset found: dataset/val
✓ Successfully imported model: SDCNet2DMaskStandalone
✓ Model created successfully
  Total parameters: 48,404,382
✓ Datasets created successfully
  Training samples: XXX
  Validation samples: XXX
✓ Data loaders created successfully
✓ Optimizer created successfully

=== Starting Quick Test Training ===
Epochs: 2
Batch size: 2
Training batches per epoch: 3
Validation batches per epoch: 2

--- Epoch 1/2 ---
  Train batch 1/3: loss = X.XXXX
  Train batch 2/3: loss = X.XXXX
  Train batch 3/3: loss = X.XXXX
  Average training loss: X.XXXX
  Val batch 1/2: loss = X.XXXX
  Val batch 2/2: loss = X.XXXX
  Average validation loss: X.XXXX

--- Epoch 2/2 ---
  Train batch 1/3: loss = X.XXXX
  Train batch 2/3: loss = X.XXXX
  Train batch 3/3: loss = X.XXXX
  Average training loss: X.XXXX
  Val batch 1/2: loss = X.XXXX
  Val batch 2/2: loss = X.XXXX
  Average validation loss: X.XXXX

✓ Model saved to: ./quick_test_results/quick_test_YYYYMMDD_HHMMSS/quick_test_checkpoint.pth

🎉 QUICK TEST COMPLETED SUCCESSFULLY!
✓ Model training works correctly
✓ Dataset loading works correctly
✓ Loss computation is stable
✓ Model can be saved and loaded
```

## Troubleshooting

### Common Issues

1. **Dataset Not Found**
   ```
   ❌ Training dataset not found: dataset/train
   ```
   **Solution**: Create the proper dataset structure as shown above.

2. **Import Errors**
   ```
   ❌ Failed to import modules: No module named 'models'
   ```
   **Solution**: Ensure you're running from the SDCNet root directory.

3. **CUDA Errors**
   ```
   ❌ CUDA out of memory
   ```
   **Solution**: The quick test uses small batch sizes, but if you still get CUDA errors, you can force CPU mode by setting `CUDA_VISIBLE_DEVICES=""`.

4. **FlowNet2 Checkpoint Missing**
   ```
   ❌ FlowNet2 checkpoint not found
   ```
   **Solution**: Download the FlowNet2 checkpoint and place it in `pretrained_models/`.

### Getting Help

If the quick test fails:

1. **Check the error message** - it will tell you exactly what went wrong
2. **Verify your dataset structure** - most issues are related to incorrect dataset organization
3. **Check your environment** - ensure all dependencies are installed
4. **Review the full error traceback** - it provides detailed information about the failure

## Next Steps

Once the quick test passes successfully:

1. **Prepare your full dataset** with all training and validation data
2. **Run full training** using:
   ```bash
   python main_mask.py --model SDCNet2DMaskStandalone --epochs 100
   ```
3. **Monitor training progress** and adjust hyperparameters as needed

## Advanced Usage

You can customize the quick test with additional parameters:

```bash
python quick_test_training.py \
    --model SDCNet2DMaskStandalone \
    --epochs 3 \
    --batch_size 4 \
    --train_n_batches 5 \
    --val_n_batches 3 \
    --crop_size 256 256
```

Available parameters:
- `--epochs`: Number of training epochs
- `--batch_size`: Batch size for training
- `--train_n_batches`: Number of training batches per epoch
- `--val_n_batches`: Number of validation batches per epoch
- `--crop_size`: Image crop size (height width)
- `--lr`: Learning rate
- `--save`: Directory to save results

This allows you to test with different configurations before committing to a full training run.
