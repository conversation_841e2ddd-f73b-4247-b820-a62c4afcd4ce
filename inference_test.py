#!/usr/bin/env python
"""
SDCNet2D Inference Script for Test Set

This script loads a trained SDCNet2D model and generates predictions on the test set.
It saves predicted frames in organized folders for easy comparison and video generation.

Usage:
    python inference_test.py --model_path ./results/checkpoints/best_model.pth --test_file dataset/test
"""

import os
import sys
import argparse
import importlib.util
from datetime import datetime
import cv2
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from tqdm import tqdm

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def import_standalone_model():
    """Import the standalone model directly without going through __init__.py"""
    spec = importlib.util.spec_from_file_location(
        "sdc_net2d_mask_standalone",
        "models/sdc_net2d_mask_standalone.py"
    )
    standalone_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(standalone_module)
    return standalone_module.SDCNet2DMaskStandalone

def create_args():
    """Create argument parser for inference."""
    parser = argparse.ArgumentParser(description='SDCNet2D Inference on Test Set')

    # Model and data paths
    parser.add_argument('--model_path', required=True, help='Path to trained model checkpoint')
    parser.add_argument('--test_file', required=True, help='Test dataset path')
    parser.add_argument('--output_dir', default=None, help='Output directory (auto-generated if not provided)')

    # Model parameters (should match training)
    parser.add_argument('--sequence_length', type=int, default=3, help='Sequence length')
    parser.add_argument('--crop_size', type=int, nargs=2, default=[256, 256], help='Crop size [H, W]')
    parser.add_argument('--rgb_max', type=float, default=255.0, help='RGB max value')
    parser.add_argument('--stride', type=int, default=64, help='Stride')
    parser.add_argument('--sample_rate', type=int, default=1, help='Sample rate')
    parser.add_argument('--start_index', type=int, default=0, help='Start index')

    # Inference parameters
    parser.add_argument('--batch_size', type=int, default=1, help='Batch size for inference')
    parser.add_argument('--workers', type=int, default=2, help='Number of workers')
    parser.add_argument('--device', type=str, default='auto', help='Device (cuda/cpu/auto)')

    # FlowNet2 checkpoint (required for model initialization)
    parser.add_argument('--flownet2_checkpoint',
                       default='pretrained_models/FlowNet2_checkpoint.pth.tar',
                       help='FlowNet2 checkpoint path')

    return parser.parse_args()

def load_model(args, device):
    """Load the trained model."""
    print("Loading model...")

    # Import and create model
    SDCNet2DMaskStandalone = import_standalone_model()
    model = SDCNet2DMaskStandalone(args)

    # Load checkpoint
    if not os.path.exists(args.model_path):
        raise FileNotFoundError(f"Model checkpoint not found: {args.model_path}")

    checkpoint = torch.load(args.model_path, map_location=device)

    # Handle different checkpoint formats
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}")
    else:
        model.load_state_dict(checkpoint, strict=False)
        print("Loaded model state dict")

    model = model.to(device)
    model.eval()

    print(f"Model loaded successfully on {device}")
    return model

def create_test_dataset(args):
    """Create test dataset."""
    print("Creating test dataset...")

    from datasets.mask_frame_loader import MaskFrameLoader

    # Create test dataset (use is_training=False for proper inference)
    test_dataset = MaskFrameLoader(args, args.test_file, is_training=False)
    print(f"Test dataset: {len(test_dataset)} sequences")

    # Data loader
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.workers,
        pin_memory=True
    )

    return test_loader, test_dataset

def save_prediction(pred_image, target_image, input_files, mask_files, output_dir, batch_idx, item_idx):
    """Save predicted and target images."""
    # Convert tensors to numpy arrays
    pred_np = pred_image[item_idx].cpu().numpy().transpose(1, 2, 0)  # [H, W, C]
    target_np = target_image[item_idx].cpu().numpy().transpose(1, 2, 0)  # [H, W, C]

    # Debug: print value ranges
    if batch_idx == 0 and item_idx == 0:  # Only for first image
        print(f"  DEBUG - Pred range: [{pred_np.min():.3f}, {pred_np.max():.3f}]")
        print(f"  DEBUG - Target range: [{target_np.min():.3f}, {target_np.max():.3f}]")
        print(f"  DEBUG - Pred shape: {pred_np.shape}")
        print(f"  DEBUG - Target shape: {target_np.shape}")

    # Denormalize (assuming images are in [0, 255] range)
    pred_np = np.clip(pred_np, 0, 255).astype(np.uint8)
    target_np = np.clip(target_np, 0, 255).astype(np.uint8)

    # Convert RGB to BGR for OpenCV
    pred_bgr = cv2.cvtColor(pred_np, cv2.COLOR_RGB2BGR)
    target_bgr = cv2.cvtColor(target_np, cv2.COLOR_RGB2BGR)

    # Extract video name from file path
    target_file = input_files[item_idx][-1]  # Last file in sequence (target)
    video_name = os.path.basename(os.path.dirname(target_file))
    frame_name = os.path.basename(target_file)

    # Create video-specific directories
    pred_video_dir = os.path.join(output_dir, "predicted", video_name)
    gt_video_dir = os.path.join(output_dir, "ground_truth", video_name)

    os.makedirs(pred_video_dir, exist_ok=True)
    os.makedirs(gt_video_dir, exist_ok=True)

    # Save images
    pred_path = os.path.join(pred_video_dir, frame_name)
    gt_path = os.path.join(gt_video_dir, frame_name)

    cv2.imwrite(pred_path, pred_bgr)
    cv2.imwrite(gt_path, target_bgr)

    return video_name, frame_name

def run_inference(args):
    """Run inference on test set."""
    # Setup device
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)

    print(f"Using device: {device}")

    # Create output directory
    if args.output_dir is None:
        timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        args.output_dir = f"predicted_{timestamp}"

    os.makedirs(args.output_dir, exist_ok=True)
    print(f"Output directory: {args.output_dir}")

    # Load model
    model = load_model(args, device)

    # Create test dataset
    test_loader, test_dataset = create_test_dataset(args)

    # Run inference
    print("Running inference...")
    video_stats = {}

    with torch.no_grad():
        pbar = tqdm(test_loader, desc='Inference')

        for batch_idx, batch in enumerate(pbar):
            # Move data to device (only tensors)
            for key in batch:
                if key in ['image', 'mask']:
                    if isinstance(batch[key], list):
                        batch[key] = [item.to(device) for item in batch[key]]
                    else:
                        batch[key] = batch[key].to(device)

            # Forward pass
            try:
                # Debug: Print tensor shapes
                if batch_idx == 0:
                    print(f"DEBUG - Batch shapes:")
                    print(f"  Images: {len(batch['image'])} items")
                    for i, img in enumerate(batch['image']):
                        print(f"    Image {i}: {img.shape}")
                    print(f"  Masks: {len(batch['mask'])} items")
                    for i, mask in enumerate(batch['mask']):
                        print(f"    Mask {i}: {mask.shape}")

                losses, pred_image, target_image = model(batch)

                # Save predictions for each item in batch
                for item_idx in range(pred_image.shape[0]):
                    video_name, frame_name = save_prediction(
                        pred_image, target_image,
                        batch['input_files'], batch['mask_files'],
                        args.output_dir, batch_idx, item_idx
                    )

                    # Update statistics
                    if video_name not in video_stats:
                        video_stats[video_name] = 0
                    video_stats[video_name] += 1

                # Update progress
                pbar.set_postfix({
                    'Videos': len(video_stats),
                    'Total Frames': sum(video_stats.values())
                })

            except Exception as e:
                print(f"Error processing batch {batch_idx}: {e}")
                continue

    # Print summary
    print(f"\n🎉 Inference completed!")
    print(f"Output directory: {args.output_dir}")
    print(f"Videos processed: {len(video_stats)}")
    print(f"Total frames: {sum(video_stats.values())}")

    print(f"\nFrames per video:")
    for video_name, count in sorted(video_stats.items()):
        print(f"  {video_name}: {count} frames")

    # Save summary
    summary_path = os.path.join(args.output_dir, "inference_summary.txt")
    with open(summary_path, 'w') as f:
        f.write(f"SDCNet2D Inference Summary\n")
        f.write(f"========================\n")
        f.write(f"Model: {args.model_path}\n")
        f.write(f"Test dataset: {args.test_file}\n")
        f.write(f"Total videos: {len(video_stats)}\n")
        f.write(f"Total frames: {sum(video_stats.values())}\n")
        f.write(f"Timestamp: {datetime.now()}\n\n")

        f.write(f"Frames per video:\n")
        for video_name, count in sorted(video_stats.items()):
            f.write(f"  {video_name}: {count} frames\n")

    print(f"Summary saved to: {summary_path}")

    return args.output_dir, video_stats

def main():
    """Main function."""
    args = create_args()

    print("=" * 60)
    print("SDCNet2D Test Set Inference")
    print("=" * 60)
    print(f"Model: {args.model_path}")
    print(f"Test dataset: {args.test_file}")
    print(f"Sequence length: {args.sequence_length}")
    print(f"Crop size: {args.crop_size}")
    print("=" * 60)

    # Check inputs
    if not os.path.exists(args.model_path):
        print(f"❌ Model checkpoint not found: {args.model_path}")
        return False

    if not os.path.exists(args.test_file):
        print(f"❌ Test dataset not found: {args.test_file}")
        return False

    try:
        output_dir, video_stats = run_inference(args)
        print(f"\n✅ Inference completed successfully!")
        print(f"Next step: Run video generation script on {output_dir}")
        return True

    except Exception as e:
        print(f"❌ Inference failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
