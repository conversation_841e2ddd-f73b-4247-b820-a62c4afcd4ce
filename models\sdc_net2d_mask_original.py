#!/usr/bin/env python
"""
SDCNet2D with Mask Support - Original Architecture

This version is IDENTICAL to the original SDCNet2D except for:
1. Additional 3 mask channels (t-1, t, t+1)
2. Input channels: 8 (original) + 3 (masks) = 11 total

Everything else remains exactly the same as the original.
"""

from __future__ import division
from __future__ import print_function

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import init

def conv2d(in_planes, out_planes, kernel_size=3, stride=1, padding=None, dilation=1, bias=True):
    """2D convolution layer."""
    if padding is None:
        padding = (kernel_size - 1) // 2
    return nn.Conv2d(in_planes, out_planes, kernel_size=kernel_size, stride=stride,
                     padding=padding, dilation=dilation, bias=bias)

def deconv2d(in_planes, out_planes, kernel_size=4, stride=2, padding=1, bias=True):
    """2D deconvolution layer."""
    return nn.ConvTranspose2d(in_planes, out_planes, kernel_size=kernel_size,
                              stride=stride, padding=padding, bias=bias)

class NativeResample2d(nn.Module):
    """Native PyTorch implementation of 2D resampling."""

    def __init__(self, bilinear=True):
        super(NativeResample2d, self).__init__()
        self.bilinear = bilinear

    def forward(self, input1, input2):
        """
        Resample input1 using flow field input2.

        COMPLETELY REWRITTEN: Proper implementation to match resample2d_cuda exactly.

        Args:
            input1: Input tensor [B, C, H, W]
            input2: Flow field [B, 2, H, W] in pixel coordinates
        """
        B, C, H, W = input1.shape

        # Create coordinate grids
        # Note: torch.meshgrid creates grids where:
        # - grid_x[i, j] = j (column index)
        # - grid_y[i, j] = i (row index)
        grid_y, grid_x = torch.meshgrid(
            torch.arange(H, dtype=input1.dtype, device=input1.device),
            torch.arange(W, dtype=input1.dtype, device=input1.device),
            indexing='ij'
        )

        # Expand grids to batch size
        grid_x = grid_x.unsqueeze(0).expand(B, -1, -1)  # [B, H, W]
        grid_y = grid_y.unsqueeze(0).expand(B, -1, -1)  # [B, H, W]

        # Apply flow: new coordinates = old coordinates - flow
        # CORRECTED: Flow indicates where to sample FROM, not where to move TO
        # input2[:, 0] is x-flow, input2[:, 1] is y-flow
        new_x = grid_x - input2[:, 0]  # [B, H, W]
        new_y = grid_y - input2[:, 1]  # [B, H, W]

        # Normalize coordinates to [-1, 1] range for grid_sample
        # grid_sample expects:
        # - x coordinates in range [-1, 1] where -1 is leftmost, 1 is rightmost
        # - y coordinates in range [-1, 1] where -1 is topmost, 1 is bottommost
        norm_x = 2.0 * new_x / (W - 1) - 1.0
        norm_y = 2.0 * new_y / (H - 1) - 1.0

        # Stack coordinates: grid_sample expects [..., 2] where last dim is [x, y]
        grid = torch.stack([norm_x, norm_y], dim=-1)  # [B, H, W, 2]

        # Apply warping
        mode = 'bilinear' if self.bilinear else 'nearest'
        output = F.grid_sample(
            input1,
            grid,
            mode=mode,
            padding_mode='border',
            align_corners=True
        )

        return output

class NativeFlowNet2Original(nn.Module):
    """Original FlowNet2 - UNCHANGED."""

    def __init__(self, args):
        super(NativeFlowNet2Original, self).__init__()
        self.rgb_max = args.rgb_max

        # Encoder - IDENTICAL to original
        self.conv1 = conv2d(6, 64, kernel_size=7, stride=2)
        self.conv2 = conv2d(64, 128, kernel_size=5, stride=2)
        self.conv3 = conv2d(128, 256, kernel_size=5, stride=2)
        self.conv3_1 = conv2d(256, 256)
        self.conv4 = conv2d(256, 512, stride=2)
        self.conv4_1 = conv2d(512, 512)
        self.conv5 = conv2d(512, 512, stride=2)
        self.conv5_1 = conv2d(512, 512)
        self.conv6 = conv2d(512, 1024, stride=2)
        self.conv6_1 = conv2d(1024, 1024)

        # Decoder - IDENTICAL to original
        self.deconv5 = deconv2d(1024, 512)
        self.deconv4 = deconv2d(1024, 256)
        self.deconv3 = deconv2d(768, 128)
        self.deconv2 = deconv2d(384, 64)
        self.deconv1 = deconv2d(192, 32)

        # Flow prediction
        self.flow_pred = nn.Conv2d(32, 2, kernel_size=3, padding=1)

        # Initialize weights
        for m in self.modules():
            if isinstance(m, nn.Conv2d) or isinstance(m, nn.ConvTranspose2d):
                if m.bias is not None:
                    init.uniform_(m.bias)
                init.xavier_uniform_(m.weight)

    def forward(self, inputs):
        """Forward pass - IDENTICAL to original."""
        B, C, T, H, W = inputs.shape

        # Reshape to [B, 6, H, W] - EXACTLY like original
        x = inputs.view(B, C * T, H, W)

        # Normalize - EXACTLY like original
        rgb_mean = x.view(B, C * T, -1).mean(dim=-1).view(B, C * T, 1, 1)
        x = (x - rgb_mean) / self.rgb_max

        # Encoder
        conv1 = self.conv1(x)
        conv2 = self.conv2(conv1)
        conv3 = self.conv3_1(self.conv3(conv2))
        conv4 = self.conv4_1(self.conv4(conv3))
        conv5 = self.conv5_1(self.conv5(conv4))
        conv6 = self.conv6_1(self.conv6(conv5))

        # Decoder
        deconv5 = self.deconv5(conv6)
        concat5 = torch.cat([conv5, deconv5], dim=1)

        deconv4 = self.deconv4(concat5)
        concat4 = torch.cat([conv4, deconv4], dim=1)

        deconv3 = self.deconv3(concat4)
        concat3 = torch.cat([conv3, deconv3], dim=1)

        deconv2 = self.deconv2(concat3)
        concat2 = torch.cat([conv2, deconv2], dim=1)

        deconv1 = self.deconv1(concat2)

        # Flow prediction
        flow = self.flow_pred(deconv1)

        return flow

class SDCNet2DMaskOriginal(nn.Module):
    """
    SDCNet2D with mask support - IDENTICAL to original except for mask channels.

    Changes from original:
    1. Input channels: 8 → 11 (added 3 mask channels)
    2. Input: 2 frames (t-1, t) + 3 masks (t-1, t, t+1)
    3. Output: 1 frame (t+1)

    Everything else is IDENTICAL to original SDCNet2D.
    """

    def __init__(self, args):
        super(SDCNet2DMaskOriginal, self).__init__()

        self.rgb_max = args.rgb_max
        self.sequence_length = 2  # FIXED: Original uses 2 frames

        # Calculate input channels - IDENTICAL to original + masks
        factor = 2
        # Original: sequence_length * 3 + (sequence_length - 1) * 2 = 2*3 + 1*2 = 8
        # + 3 masks = 11 total
        input_channels = self.sequence_length * 3 + (self.sequence_length - 1) * 2 + 3

        # Network architecture - IDENTICAL to original SDCNet2D
        self.conv1 = conv2d(input_channels, 64 // factor, kernel_size=7, stride=2)
        self.conv2 = conv2d(64 // factor, 128 // factor, kernel_size=5, stride=2)
        self.conv3 = conv2d(128 // factor, 256 // factor, kernel_size=5, stride=2)
        self.conv3_1 = conv2d(256 // factor, 256 // factor)
        self.conv4 = conv2d(256 // factor, 512 // factor, stride=2)
        self.conv4_1 = conv2d(512 // factor, 512 // factor)
        self.conv5 = conv2d(512 // factor, 512 // factor, stride=2)
        self.conv5_1 = conv2d(512 // factor, 512 // factor)
        self.conv6 = conv2d(512 // factor, 1024 // factor, stride=2)
        self.conv6_1 = conv2d(1024 // factor, 1024 // factor)

        self.deconv5 = deconv2d(1024 // factor, 512 // factor)
        self.deconv4 = deconv2d(1024 // factor, 256 // factor)
        self.deconv3 = deconv2d(768 // factor, 128 // factor)
        self.deconv2 = deconv2d(384 // factor, 64 // factor)
        self.deconv1 = deconv2d(192 // factor, 32 // factor)
        self.deconv0 = deconv2d(96 // factor, 16 // factor)

        # Output head - IDENTICAL to original
        self.final_flow = nn.Conv2d(input_channels + 16 // factor, 2,
                                   kernel_size=3, stride=1, padding=1, bias=True)

        # Initialize parameters - IDENTICAL to original
        for m in self.modules():
            if isinstance(m, nn.Conv2d) or isinstance(m, nn.ConvTranspose2d):
                if m.bias is not None:
                    init.uniform_(m.bias)
                init.xavier_uniform_(m.weight)

        # Native implementations
        self.flownet2 = NativeFlowNet2Original(args)
        self.warp_nn = NativeResample2d(bilinear=False)
        self.warp_bilinear = NativeResample2d(bilinear=True)

        # Loss functions
        self.L1Loss = nn.L1Loss()

        # Normalization parameters - IDENTICAL to original
        flow_mean = torch.FloatTensor([-0.94427323, -1.23077035]).view(1, 2, 1, 1)
        flow_std = torch.FloatTensor([13.77204132, 7.47463894]).view(1, 2, 1, 1)

        self.register_buffer('flow_mean', flow_mean)
        self.register_buffer('flow_std', flow_std)

        # For compatibility
        self.ignore_keys = ['flownet2']

    def interframe_optical_flow(self, input_images):
        """Compute optical flow - IDENTICAL to original."""
        flownet2_inputs = torch.stack(
            [torch.cat([input_images[i + 1].unsqueeze(2), input_images[i].unsqueeze(2)], dim=2) for i in
             range(0, self.sequence_length - 1)], dim=0).contiguous()

        batch_size, channel_count, height, width = input_images[0].shape
        flownet2_inputs_flattened = flownet2_inputs.view(-1, channel_count, 2, height, width)
        flownet2_outputs = [self.flownet2(flownet2_input) for flownet2_input in
                            torch.chunk(flownet2_inputs_flattened, self.sequence_length - 1)]

        return flownet2_outputs

    def network_output(self, input_images, input_flows, input_masks):
        """Network forward pass - IDENTICAL to original + masks."""
        # Normalize input flows - IDENTICAL to original
        input_flows = [(input_flow - self.flow_mean) / (3 * self.flow_std) for
                       input_flow in input_flows]

        # Normalize input via flownet2-type normalisation - IDENTICAL to original
        concated_images = torch.cat([image.unsqueeze(2) for image in input_images], dim=2).contiguous()
        rgb_mean = concated_images.view(concated_images.size()[:2] + (-1,)).mean(dim=-1).view(
            concated_images.size()[:2] + 2 * (1,))
        input_images = [(input_image - rgb_mean) / self.rgb_max for input_image in input_images]
        bsize, channels, height, width = input_flows[0].shape

        # Get original image dimensions before resizing
        orig_height, orig_width = input_images[0].shape[2], input_images[0].shape[3]

        # Atypical concatenation - IDENTICAL to original
        input_images_stacked = torch.cat([input_image.unsqueeze(2) for input_image in input_images], dim=2)
        input_images_concat = input_images_stacked.contiguous().view(bsize, -1, orig_height, orig_width)

        # Resize images to match flow resolution
        if orig_height != height or orig_width != width:
            input_images_concat = F.interpolate(input_images_concat, size=(height, width), mode='bilinear', align_corners=True)

        # Concatenate flows - IDENTICAL to original
        input_flows_stacked = torch.cat([input_flow.unsqueeze(2) for input_flow in input_flows], dim=2)
        input_flows_concat = input_flows_stacked.contiguous().view(bsize, -1, height, width)

        # NEW: Handle masks
        resized_masks = []
        for mask in input_masks:
            if mask.shape[2] != height or mask.shape[3] != width:
                resized_mask = F.interpolate(mask, size=(height, width), mode='bilinear', align_corners=True)
                resized_masks.append(resized_mask)
            else:
                resized_masks.append(mask)
        input_masks_concat = torch.cat(resized_masks, dim=1)

        # Network input - IDENTICAL order to original + masks at the end
        images_and_flows = torch.cat((input_flows_concat, input_images_concat), dim=1)
        full_input = torch.cat((images_and_flows, input_masks_concat), dim=1)

        # Encoder-decoder with size matching
        out_conv1 = self.conv1(full_input)
        out_conv2 = self.conv2(out_conv1)
        out_conv3 = self.conv3_1(self.conv3(out_conv2))
        out_conv4 = self.conv4_1(self.conv4(out_conv3))
        out_conv5 = self.conv5_1(self.conv5(out_conv4))
        out_conv6 = self.conv6_1(self.conv6(out_conv5))

        out_deconv5 = self.deconv5(out_conv6)
        # Ensure spatial dimensions match before concatenation
        if out_conv5.shape[2:] != out_deconv5.shape[2:]:
            out_deconv5 = F.interpolate(out_deconv5, size=out_conv5.shape[2:], mode='bilinear', align_corners=True)
        concat5 = torch.cat((out_conv5, out_deconv5), 1)

        out_deconv4 = self.deconv4(concat5)
        if out_conv4.shape[2:] != out_deconv4.shape[2:]:
            out_deconv4 = F.interpolate(out_deconv4, size=out_conv4.shape[2:], mode='bilinear', align_corners=True)
        concat4 = torch.cat((out_conv4, out_deconv4), 1)

        out_deconv3 = self.deconv3(concat4)
        if out_conv3.shape[2:] != out_deconv3.shape[2:]:
            out_deconv3 = F.interpolate(out_deconv3, size=out_conv3.shape[2:], mode='bilinear', align_corners=True)
        concat3 = torch.cat((out_conv3, out_deconv3), 1)

        out_deconv2 = self.deconv2(concat3)
        if out_conv2.shape[2:] != out_deconv2.shape[2:]:
            out_deconv2 = F.interpolate(out_deconv2, size=out_conv2.shape[2:], mode='bilinear', align_corners=True)
        concat2 = torch.cat((out_conv2, out_deconv2), 1)

        out_deconv1 = self.deconv1(concat2)
        if out_conv1.shape[2:] != out_deconv1.shape[2:]:
            out_deconv1 = F.interpolate(out_deconv1, size=out_conv1.shape[2:], mode='bilinear', align_corners=True)
        concat1 = torch.cat((out_conv1, out_deconv1), 1)

        out_deconv0 = self.deconv0(concat1)

        # Final predictions - IDENTICAL to original
        concat0 = torch.cat((full_input, out_deconv0), 1)
        output_flow = self.final_flow(concat0)

        # Denormalize flow - IDENTICAL to original
        flow_prediction = 3 * self.flow_std * output_flow + self.flow_mean

        return flow_prediction

    def prepare_inputs(self, input_dict):
        """Prepare inputs - MODIFIED for 2 frames + 3 masks."""
        images = input_dict['image']
        masks = input_dict['mask']

        # Take first 2 images (t-1, t)
        input_images = images[:2]
        # Take all 3 masks (t-1, t, t+1)
        input_masks = masks[:3]

        # Target is 3rd image (t+1)
        target_image = images[2]
        last_image = (input_images[-1]).clone()

        return input_images, input_masks, last_image, target_image

    def forward(self, input_dict, label_image=None):
        """Forward pass - IDENTICAL to original SDCNet2D."""
        input_images, input_masks, last_image, target_image = self.prepare_inputs(input_dict)

        input_flows = self.interframe_optical_flow(input_images)
        flow_prediction = self.network_output(input_images, input_flows, input_masks)

        # Resize flow to match last_image resolution for warping
        if flow_prediction.shape[2] != last_image.shape[2] or flow_prediction.shape[3] != last_image.shape[3]:
            scale_h = last_image.shape[2] / flow_prediction.shape[2]
            scale_w = last_image.shape[3] / flow_prediction.shape[3]

            flow_resized = F.interpolate(flow_prediction, size=(last_image.shape[2], last_image.shape[3]),
                                       mode='bilinear', align_corners=True)

            # Scale flow values to account for resolution change
            flow_resized[:, 0, :, :] *= scale_w  # x component
            flow_resized[:, 1, :, :] *= scale_h  # y component
        else:
            flow_resized = flow_prediction

        # Warp last image using predicted flow
        image_prediction = self.warp_bilinear(last_image, flow_resized)

        if label_image is not None:
            label_prediction = self.warp_nn(label_image, flow_resized)

        # Calculate losses - IDENTICAL to original SDCNet2D
        losses = {}
        losses['color'] = self.L1Loss(image_prediction/self.rgb_max, target_image/self.rgb_max)
        losses['color_gradient'] = self.L1Loss(torch.abs(image_prediction[...,1:] - image_prediction[...,:-1]),
                                               torch.abs(target_image[...,1:] - target_image[...,:-1])) + \
                                   self.L1Loss(torch.abs(image_prediction[..., 1:,:] - image_prediction[..., :-1,:]),
                                               torch.abs(target_image[..., 1:,:] - target_image[..., :-1,:]))
        losses['flow_smoothness'] = self.L1Loss(flow_prediction[...,1:], flow_prediction[...,:-1]) + \
                                    self.L1Loss(flow_prediction[..., 1:,:], flow_prediction[..., :-1,:])

        # IDENTICAL loss weights to original SDCNet2D
        losses['tot'] = 0.7 * losses['color'] + 0.2 * losses['color_gradient'] + 0.1 * losses['flow_smoothness']

        if label_image is not None:
            image_prediction = label_prediction

        return losses, image_prediction, target_image
