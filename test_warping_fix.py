#!/usr/bin/env python3
"""
Test script to verify the warping fix for the offset problem.
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import os
import sys

# Add models directory to path
sys.path.append('models')

from sdc_net2d_mask_original import NativeResample2d

def create_test_image():
    """Create a simple test image with a clear pattern."""
    # Create a 64x64 test image with a checkerboard pattern
    size = 64
    img = np.zeros((size, size, 3), dtype=np.uint8)
    
    # Create checkerboard pattern
    for i in range(size):
        for j in range(size):
            if (i // 8 + j // 8) % 2 == 0:
                img[i, j] = [255, 255, 255]  # White
            else:
                img[i, j] = [0, 0, 0]  # Black
    
    # Add a red square in the center to track movement
    center = size // 2
    img[center-4:center+4, center-4:center+4] = [255, 0, 0]  # Red square
    
    return img

def test_identity_warp():
    """Test that zero flow produces identical output."""
    print("Testing identity warp (zero flow)...")
    
    # Create test image
    test_img = create_test_image()
    
    # Convert to tensor [1, 3, H, W]
    img_tensor = torch.from_numpy(test_img).permute(2, 0, 1).unsqueeze(0).float()
    H, W = img_tensor.shape[2], img_tensor.shape[3]
    
    # Create zero flow [1, 2, H, W]
    zero_flow = torch.zeros(1, 2, H, W)
    
    # Test warping
    warper = NativeResample2d(bilinear=True)
    warped = warper(img_tensor, zero_flow)
    
    # Check if output is identical to input
    diff = torch.abs(warped - img_tensor).max().item()
    print(f"Max difference with zero flow: {diff}")
    
    if diff < 1e-6:
        print("✓ Identity warp test PASSED")
        return True
    else:
        print("✗ Identity warp test FAILED")
        return False

def test_translation_warp():
    """Test translation by known amounts."""
    print("\nTesting translation warp...")
    
    # Create test image
    test_img = create_test_image()
    img_tensor = torch.from_numpy(test_img).permute(2, 0, 1).unsqueeze(0).float()
    H, W = img_tensor.shape[2], img_tensor.shape[3]
    
    # Test different translations
    translations = [(5, 0), (0, 5), (3, 3), (-2, 4)]
    warper = NativeResample2d(bilinear=False)  # Use nearest neighbor for exact comparison
    
    for dx, dy in translations:
        print(f"  Testing translation: dx={dx}, dy={dy}")
        
        # Create constant flow field
        flow = torch.zeros(1, 2, H, W)
        flow[0, 0, :, :] = dx  # x displacement
        flow[0, 1, :, :] = dy  # y displacement
        
        # Warp image
        warped = warper(img_tensor, flow)
        
        # Convert back to numpy for visualization
        warped_np = warped[0].permute(1, 2, 0).numpy().astype(np.uint8)
        
        # Save for visual inspection
        os.makedirs('test_warping_results', exist_ok=True)
        Image.fromarray(warped_np).save(f'test_warping_results/translation_{dx}_{dy}.png')
        
        # Check that the red square moved correctly
        # Find red pixels in original and warped images
        original_red = (test_img[:, :, 0] > 200) & (test_img[:, :, 1] < 50) & (test_img[:, :, 2] < 50)
        warped_red = (warped_np[:, :, 0] > 200) & (warped_np[:, :, 1] < 50) & (warped_np[:, :, 2] < 50)
        
        if np.any(original_red) and np.any(warped_red):
            # Find centers of red regions
            orig_center = np.array(np.where(original_red)).mean(axis=1)
            warp_center = np.array(np.where(warped_red)).mean(axis=1)
            
            # Expected displacement (note: y, x order for numpy arrays)
            expected_displacement = np.array([dy, dx])
            actual_displacement = warp_center - orig_center
            
            error = np.linalg.norm(actual_displacement - expected_displacement)
            print(f"    Expected displacement: {expected_displacement}")
            print(f"    Actual displacement: {actual_displacement}")
            print(f"    Error: {error:.2f} pixels")
            
            if error < 1.0:
                print(f"    ✓ Translation test PASSED")
            else:
                print(f"    ✗ Translation test FAILED")
        else:
            print(f"    ? Could not find red square in one of the images")

def main():
    """Run all tests."""
    print("Testing warping implementation fix...")
    print("=" * 50)
    
    # Save original test image
    test_img = create_test_image()
    os.makedirs('test_warping_results', exist_ok=True)
    Image.fromarray(test_img).save('test_warping_results/original.png')
    print("Original test image saved to: test_warping_results/original.png")
    
    # Run tests
    identity_passed = test_identity_warp()
    test_translation_warp()
    
    print("\n" + "=" * 50)
    if identity_passed:
        print("Basic warping tests completed. Check test_warping_results/ for visual inspection.")
        print("If translations look correct, the fix should work for SDCNet.")
    else:
        print("Basic identity test failed. The warping implementation needs more work.")

if __name__ == "__main__":
    main()
