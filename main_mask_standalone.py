#!/usr/bin/env python
"""
SDCNet2D with Mask Support - Standalone Training Script

This script trains the standalone version of SDCNet2D with mask support.
It bypasses all CUDA dependencies and uses only PyTorch native operations.

Usage:
    python main_mask_standalone.py --train_file ./dataset/train/ --val_file ./dataset/val/
"""

from __future__ import division
from __future__ import print_function

import os
import sys
import time
import argparse
import importlib.util

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torch.nn.functional as F

import numpy as np
from tensorboardX import SummaryWriter
from tqdm import tqdm

# Import dataset loader directly
from datasets.mask_frame_loader import MaskFrameLoader

def import_standalone_model():
    """Import the standalone model directly without going through __init__.py"""
    spec = importlib.util.spec_from_file_location(
        "sdc_net2d_mask_standalone",
        "models/sdc_net2d_mask_standalone.py"
    )
    standalone_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(standalone_module)
    return standalone_module.SDCNet2DMaskStandalone

def create_args():
    """Create argument parser with all necessary parameters."""
    parser = argparse.ArgumentParser(description='SDCNet2D Mask Training - Standalone')

    # Dataset parameters
    parser.add_argument('--train_file', required=True, help='Training dataset path')
    parser.add_argument('--val_file', required=True, help='Validation dataset path')
    parser.add_argument('--sequence_length', type=int, default=3, help='Sequence length')
    parser.add_argument('--sample_rate', type=int, default=1, help='Sample rate')
    parser.add_argument('--crop_size', type=int, nargs=2, default=[256, 256], help='Crop size [H, W]')
    parser.add_argument('--start_index', type=int, default=0, help='Start index')
    parser.add_argument('--stride', type=int, default=64, help='Stride')

    # Model parameters
    parser.add_argument('--rgb_max', type=float, default=255.0, help='RGB max value')

    # Training parameters
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--epochs', type=int, default=500, help='Number of epochs')
    parser.add_argument('--learning_rate', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--mask_weight', type=float, default=0.2, help='Mask loss weight')

    # Logging and saving
    parser.add_argument('--save_freq', type=int, default=50, help='Save frequency (epochs)')
    parser.add_argument('--val_freq', type=int, default=10, help='Validation frequency (epochs)')
    parser.add_argument('--print_freq', type=int, default=10, help='Print frequency (batches)')
    parser.add_argument('--val_n_batches', type=int, default=None, help='Number of validation batches')

    # Early stopping
    parser.add_argument('--early_stopping', action='store_true', help='Enable early stopping')
    parser.add_argument('--patience', type=int, default=10, help='Early stopping patience (epochs)')
    parser.add_argument('--min_delta', type=float, default=0.001, help='Minimum improvement for early stopping')

    # Resume training
    parser.add_argument('--resume', type=str, help='Resume from checkpoint')
    parser.add_argument('--checkpoint_dir', type=str, default='./checkpoints', help='Checkpoint directory')
    parser.add_argument('--log_dir', type=str, default='./logs', help='Log directory')

    return parser.parse_args()

def create_datasets(args):
    """Create training and validation datasets."""
    print("Creating datasets...")

    # Training dataset
    train_dataset = MaskFrameLoader(args, args.train_file, is_training=True)
    print(f"Training dataset: {len(train_dataset)} sequences")

    # Validation dataset - use is_training=True to ensure consistent cropping
    val_dataset = MaskFrameLoader(args, args.val_file, is_training=True)
    print(f"Validation dataset: {len(val_dataset)} sequences")

    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    return train_loader, val_loader

def create_model_and_optimizer(args, device):
    """Create model and optimizer."""
    print("Creating model...")

    # Import and create model
    SDCNet2DMaskStandalone = import_standalone_model()
    model = SDCNet2DMaskStandalone(args)
    model = model.to(device)

    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")

    # Optimizer
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate, weight_decay=args.weight_decay)

    # Learning rate scheduler
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=100, gamma=0.5)

    return model, optimizer, scheduler

def train_epoch(model, train_loader, optimizer, device, epoch, args, writer):
    """Train for one epoch."""
    model.train()

    total_loss = 0.0
    total_color_loss = 0.0

    pbar = tqdm(train_loader, desc=f'Epoch {epoch}')

    for batch_idx, batch in enumerate(pbar):
        # Move data to device (only tensors)
        for key in batch:
            if key in ['image', 'mask']:  # Only move tensor data
                if isinstance(batch[key], list):
                    batch[key] = [item.to(device) for item in batch[key]]
                else:
                    batch[key] = batch[key].to(device)
            # Skip non-tensor data like 'input_files', 'mask_files', 'ishape'

        # Forward pass
        optimizer.zero_grad()
        losses, pred_image, target_image = model(batch)

        # Backward pass
        loss = losses['tot']
        loss.backward()
        optimizer.step()

        # Update statistics
        total_loss += loss.item()
        total_color_loss += losses['color'].item()

        # Update progress bar
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Color': f'{losses["color"].item():.4f}',
            'Gradient': f'{losses["color_gradient"].item():.4f}'
        })

        # Log to tensorboard
        if writer and batch_idx % args.print_freq == 0:
            step = epoch * len(train_loader) + batch_idx
            writer.add_scalar('Train/Total_Loss', loss.item(), step)
            writer.add_scalar('Train/Color_Loss', losses['color'].item(), step)
            # No mask loss to log
            writer.add_scalar('Train/Gradient_Loss', losses['color_gradient'].item(), step)
            writer.add_scalar('Train/Smoothness_Loss', losses['flow_smoothness'].item(), step)

    # Return average losses
    avg_loss = total_loss / len(train_loader)
    avg_color_loss = total_color_loss / len(train_loader)

    return avg_loss, avg_color_loss

def validate(model, val_loader, device, epoch, args, writer):
    """Validate the model."""
    model.eval()

    total_loss = 0.0
    total_color_loss = 0.0

    with torch.no_grad():
        pbar = tqdm(val_loader, desc=f'Validation {epoch}')

        for batch_idx, batch in enumerate(pbar):
            # Limit validation batches if specified
            if args.val_n_batches and batch_idx >= args.val_n_batches:
                break

            # Move data to device (only tensors)
            for key in batch:
                if key in ['image', 'mask']:  # Only move tensor data
                    if isinstance(batch[key], list):
                        batch[key] = [item.to(device) for item in batch[key]]
                    else:
                        batch[key] = batch[key].to(device)
                # Skip non-tensor data like 'input_files', 'mask_files', 'ishape'

            # Forward pass
            losses, pred_image, target_image = model(batch)

            # Update statistics
            total_loss += losses['tot'].item()
            total_color_loss += losses['color'].item()

            # Update progress bar
            pbar.set_postfix({
                'Val Loss': f'{losses["tot"].item():.4f}',
                'Val Color': f'{losses["color"].item():.4f}',
                'Val Gradient': f'{losses["color_gradient"].item():.4f}'
            })

    # Calculate averages
    num_batches = min(len(val_loader), args.val_n_batches or len(val_loader))
    avg_loss = total_loss / num_batches
    avg_color_loss = total_color_loss / num_batches

    # Log to tensorboard
    if writer:
        writer.add_scalar('Val/Total_Loss', avg_loss, epoch)
        writer.add_scalar('Val/Color_Loss', avg_color_loss, epoch)

    return avg_loss, avg_color_loss

def save_checkpoint(model, optimizer, scheduler, epoch, loss, args):
    """Save model checkpoint."""
    os.makedirs(args.checkpoint_dir, exist_ok=True)

    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'loss': loss,
        'args': args
    }

    # Save latest checkpoint
    latest_path = os.path.join(args.checkpoint_dir, 'latest.pth')
    torch.save(checkpoint, latest_path)

    # Save epoch checkpoint
    if epoch % args.save_freq == 0:
        epoch_path = os.path.join(args.checkpoint_dir, f'model_epoch_{epoch}.pth')
        torch.save(checkpoint, epoch_path)
        print(f"Checkpoint saved: {epoch_path}")

def load_checkpoint(model, optimizer, scheduler, args):
    """Load model checkpoint."""
    if not args.resume or not os.path.exists(args.resume):
        return 0

    print(f"Loading checkpoint: {args.resume}")
    checkpoint = torch.load(args.resume, map_location='cpu')

    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

    start_epoch = checkpoint['epoch'] + 1
    print(f"Resumed from epoch {start_epoch}")

    return start_epoch

def main():
    """Main training function."""
    args = create_args()

    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Create datasets
    train_loader, val_loader = create_datasets(args)

    # Create model and optimizer
    model, optimizer, scheduler = create_model_and_optimizer(args, device)

    # Setup logging
    os.makedirs(args.log_dir, exist_ok=True)
    writer = SummaryWriter(args.log_dir)

    # Load checkpoint if resuming
    start_epoch = load_checkpoint(model, optimizer, scheduler, args)

    print(f"\nStarting training from epoch {start_epoch}")
    print(f"Total epochs: {args.epochs}")
    print(f"Batch size: {args.batch_size}")
    print(f"Learning rate: {args.learning_rate}")
    print(f"Mask weight: {args.mask_weight}")
    print("=" * 50)

    # Training loop
    best_val_loss = float('inf')
    patience_counter = 0
    early_stop = False

    for epoch in range(start_epoch, args.epochs):
        if early_stop:
            print(f"\nEarly stopping triggered at epoch {epoch}")
            break
        # Training
        train_loss, train_color_loss = train_epoch(
            model, train_loader, optimizer, device, epoch, args, writer
        )

        # Validation
        if epoch % args.val_freq == 0:
            val_loss, val_color_loss = validate(
                model, val_loader, device, epoch, args, writer
            )

            print(f"\nEpoch {epoch}:")
            print(f"  Train - Total: {train_loss:.4f}, Color: {train_color_loss:.4f}")
            print(f"  Val   - Total: {val_loss:.4f}, Color: {val_color_loss:.4f}")

            # Save best model and early stopping logic
            if val_loss < best_val_loss - args.min_delta:
                best_val_loss = val_loss
                patience_counter = 0  # Reset patience
                os.makedirs(args.checkpoint_dir, exist_ok=True)  # Ensure directory exists
                best_path = os.path.join(args.checkpoint_dir, 'best_model.pth')
                torch.save(model.state_dict(), best_path)
                print(f"  New best model saved: {best_path}")
            else:
                patience_counter += 1
                print(f"  No improvement. Patience: {patience_counter}/{args.patience}")

                # Check for early stopping
                if args.early_stopping and patience_counter >= args.patience:
                    print(f"  Early stopping triggered! No improvement for {args.patience} validations.")
                    early_stop = True

        # Save checkpoint
        save_checkpoint(model, optimizer, scheduler, epoch, train_loss, args)

        # Update learning rate
        scheduler.step()

    print("\nTraining completed!")
    writer.close()

if __name__ == '__main__':
    main()
