# File: populate_dataset_davis.py
"""
Popola il dataset nel formato SDCNet2D (frame + maschere binarie) partendo da DAVIS 2016 / 2017.

Esempio d’uso (UNA SOLA LINEA, pronta per copia-incolla):
    python populate_dataset_davis.py ^
        --frames-root "C:\\...\\VIPSeg\\DAVIS-data\\DAVIS\\JPEGImages\\480p" ^
        --masks-root  "C:\\...\\VIPSeg\\DAVIS-data\\DAVIS\\Annotations\\480p" ^
        --split-root  "C:\\...\\VIPSeg\\DAVIS-2017\\dataset_davis_2016_2017" ^
        --out-root    "C:\\...\\sdcnet\\dataset" ^
        --width 320 --height 256
"""

import os, re, argparse, shutil, json
from collections import defaultdict
from PIL import Image
from pathlib import Path
from tqdm import tqdm

def parse_args():
    p = argparse.ArgumentParser(description="Populate SDCNet2D dataset from DAVIS")
    p.add_argument("--frames-root", required=True, help="JPEGImages/480p path")
    p.add_argument("--masks-root",  required=True, help="Annotations/480p path")
    p.add_argument("--split-root",  required=True, help="dataset_davis_2016_2017 path (contiene train/val/test)")
    p.add_argument("--out-root",    required=True, help="Destinazione finale del dataset")
    p.add_argument("--width",  type=int, default=320)
    p.add_argument("--height", type=int, default=256)
    p.add_argument("--seed",   type=int, default=42)
    return p.parse_args()

def resize(img, w, h, is_mask=False):
    interp = Image.NEAREST if is_mask else Image.BILINEAR
    return img.resize((w, h), interp)

def ensure_dir(p):
    os.makedirs(p, exist_ok=True)
    return p

def load_or_build_split_lists(split_root):
    txts = {s: Path(split_root, f"{s}.txt") for s in ("train","val","test")}
    if all(t.exists() for t in txts.values()):
        return {s: [l.strip() for l in t.read_text().splitlines() if l.strip()] for s,t in txts.items()}
    # build from directory names in Y
    split_lists = {}
    pat = re.compile(r"^(?P<name>.+)_\d{5}\.png$")
    for split in ("train","val","test"):
        names=set()
        for f in Path(split_root, split, "Y").glob("*.png"):
            m=pat.match(f.name)
            if m: names.add(m.group("name"))
        split_lists[split]=sorted(names)
        txts[split].write_text("\n".join(split_lists[split]))
    print("Generati train/val/test.txt con le sequenze trovate.")
    return split_lists

def copy_sequence(video, split, args):
    # sorgenti
    f_dir = Path(args.frames_root,  video)
    m_dir = Path(args.masks_root,   video)
    if not f_dir.is_dir() or not m_dir.is_dir():
        print(f"⚠  Sequenza mancante: {video}")
        return
    # destinazioni
    x_frames = ensure_dir(Path(args.out_root, split, "X", video))
    x_masks  = ensure_dir(Path(args.out_root, split, "X", f"{video}_binary_ground_truth"))
    y_frames = ensure_dir(Path(args.out_root, split, "Y", video))

    frames = sorted(f_dir.glob("*.jpg")) + sorted(f_dir.glob("*.png"))
    masks  = sorted(m_dir.glob("*.png"))
    if len(frames)!=len(masks):
        print(f"⚠  Numero frame/mask diverso in {video} (frame {len(frames)} vs mask {len(masks)})")

    for fr,mk in tqdm(zip(frames, masks), total=len(frames), desc=f"{split}/{video}", leave=False):
        idx = fr.stem.zfill(6) if len(fr.stem)<6 else fr.stem  # uniformità
        # frame -> train/X & train/Y
        img = resize(Image.open(fr), args.width, args.height)
        img.save(x_frames/f"{idx}.png")
        img.save(y_frames/f"{idx}.png")
        # mask -> train/X/<video>_binary_ground_truth
        mimg = resize(Image.open(mk), args.width, args.height, is_mask=True)
        mimg.save(x_masks/f"{idx}.png")

def main():
    args = parse_args()
    split_lists = load_or_build_split_lists(args.split_root)

    for split, videos in split_lists.items():
        print(f"➡  Copio {len(videos)} sequenze in {split}...")
        for v in videos:
            copy_sequence(v, split, args)

    print("✅  Dataset popolato correttamente!")

if __name__ == "__main__":
    main()
