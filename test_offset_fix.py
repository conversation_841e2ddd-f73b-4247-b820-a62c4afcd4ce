#!/usr/bin/env python3
"""
Quick test to verify if the offset fix works with a trained model.
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import os
import sys
import argparse
from datetime import datetime

# Add models directory to path
sys.path.append('models')

def create_args():
    """Create arguments for model initialization."""
    args = argparse.Namespace()
    args.rgb_max = 255.0
    args.sequence_length = 2
    return args

def load_test_images():
    """Load a few test images from the dataset."""
    # Look for existing dataset images
    dataset_paths = [
        'dataset/train/blackswan/00000.jpg',
        'dataset/train/blackswan/00001.jpg', 
        'dataset/train/blackswan/00002.jpg'
    ]
    
    images = []
    for path in dataset_paths:
        if os.path.exists(path):
            img = Image.open(path).convert('RGB')
            img_array = np.array(img)
            images.append(img_array)
        else:
            print(f"Warning: {path} not found")
    
    if len(images) < 3:
        print("Not enough test images found. Creating synthetic test images.")
        # Create synthetic test images with clear patterns
        for i in range(3):
            img = np.zeros((256, 320, 3), dtype=np.uint8)
            # Create a pattern that's easy to track
            img[100:156, 100:220] = [255, 0, 0]  # Red rectangle
            img[120:136, 120:200] = [0, 255, 0]  # Green rectangle inside
            # Add frame-specific marker
            img[10:30, 10+i*20:30+i*20] = [0, 0, 255]  # Blue marker
            images.append(img)
    
    return images

def load_test_masks():
    """Load corresponding test masks."""
    # Look for existing mask files
    mask_paths = [
        'dataset/train/blackswan/00000.png',
        'dataset/train/blackswan/00001.png',
        'dataset/train/blackswan/00002.png'
    ]
    
    masks = []
    for path in mask_paths:
        if os.path.exists(path):
            mask = Image.open(path).convert('L')
            mask_array = np.array(mask)
            masks.append(mask_array)
        else:
            print(f"Warning: {path} not found")
    
    if len(masks) < 3:
        print("Not enough test masks found. Creating synthetic masks.")
        # Create synthetic masks
        for i in range(3):
            mask = np.zeros((256, 320), dtype=np.uint8)
            # Create a mask around the red rectangle
            mask[95:161, 95:225] = 255
            masks.append(mask)
    
    return masks

def test_model_inference():
    """Test model inference with the offset fix."""
    print("Testing model inference with offset fix...")
    
    # Load model
    try:
        from sdc_net2d_mask_original import SDCNet2DMaskOriginal
        model_class = SDCNet2DMaskOriginal
        model_name = "SDCNet2DMaskOriginal"
    except ImportError:
        try:
            from sdc_net2d_mask_standalone import SDCNet2DMaskStandalone
            model_class = SDCNet2DMaskStandalone
            model_name = "SDCNet2DMaskStandalone"
        except ImportError:
            print("Error: Could not import any SDCNet model")
            return False
    
    print(f"Using model: {model_name}")
    
    # Create model
    args = create_args()
    model = model_class(args)
    model.eval()
    
    # Load test data
    images = load_test_images()
    masks = load_test_masks()
    
    print(f"Loaded {len(images)} test images and {len(masks)} test masks")
    
    # Convert to tensors
    image_tensors = []
    mask_tensors = []
    
    for img, mask in zip(images, masks):
        # Convert image to tensor [3, H, W]
        img_tensor = torch.from_numpy(img).permute(2, 0, 1).float()
        image_tensors.append(img_tensor)
        
        # Convert mask to tensor [1, H, W]
        mask_tensor = torch.from_numpy(mask).unsqueeze(0).float()
        mask_tensors.append(mask_tensor)
    
    # Create batch [1, 3, H, W] for each
    batch_images = [img.unsqueeze(0) for img in image_tensors]
    batch_masks = [mask.unsqueeze(0) for mask in mask_tensors]
    
    # Prepare input dict
    input_dict = {
        'image': batch_images,
        'mask': batch_masks
    }
    
    # Run inference
    print("Running inference...")
    with torch.no_grad():
        try:
            if model_name == "SDCNet2DMaskStandalone":
                losses, pred_image, target_image, pred_mask, target_mask = model(input_dict)
            else:
                losses, pred_image, target_image = model(input_dict)
            
            print(f"Inference successful!")
            print(f"Color loss: {losses['color']:.6f}")
            
            # Save results for visual inspection
            os.makedirs('test_offset_results', exist_ok=True)
            
            # Convert tensors to images
            pred_np = pred_image[0].permute(1, 2, 0).clamp(0, 255).numpy().astype(np.uint8)
            target_np = target_image[0].permute(1, 2, 0).clamp(0, 255).numpy().astype(np.uint8)
            input_np = batch_images[1][0].permute(1, 2, 0).clamp(0, 255).numpy().astype(np.uint8)
            
            # Save images
            Image.fromarray(input_np).save('test_offset_results/input_frame.png')
            Image.fromarray(target_np).save('test_offset_results/target_frame.png')
            Image.fromarray(pred_np).save('test_offset_results/predicted_frame.png')
            
            # Create comparison image
            comparison = np.concatenate([input_np, target_np, pred_np], axis=1)
            Image.fromarray(comparison).save('test_offset_results/comparison.png')
            
            print("Results saved to test_offset_results/")
            print("- input_frame.png: Input frame (t)")
            print("- target_frame.png: Target frame (t+1)")
            print("- predicted_frame.png: Predicted frame")
            print("- comparison.png: Side-by-side comparison")
            
            # Check for obvious offset by comparing centers
            def find_pattern_center(img):
                """Find center of red pattern in image."""
                red_mask = (img[:, :, 0] > 200) & (img[:, :, 1] < 100) & (img[:, :, 2] < 100)
                if np.any(red_mask):
                    coords = np.where(red_mask)
                    center_y = np.mean(coords[0])
                    center_x = np.mean(coords[1])
                    return center_y, center_x
                return None, None
            
            target_center = find_pattern_center(target_np)
            pred_center = find_pattern_center(pred_np)
            
            if target_center[0] is not None and pred_center[0] is not None:
                offset_y = pred_center[0] - target_center[0]
                offset_x = pred_center[1] - target_center[1]
                print(f"\nPattern center offset:")
                print(f"  Y offset: {offset_y:.1f} pixels")
                print(f"  X offset: {offset_x:.1f} pixels")
                
                if abs(offset_y) < 5 and abs(offset_x) < 5:
                    print("✓ Offset appears to be fixed (< 5 pixels)")
                    return True
                else:
                    print("✗ Significant offset still present")
                    return False
            else:
                print("? Could not detect pattern to measure offset")
                return None
                
        except Exception as e:
            print(f"Error during inference: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Run the offset fix test."""
    print("Testing SDCNet offset fix")
    print("=" * 40)
    
    result = test_model_inference()
    
    print("\n" + "=" * 40)
    if result is True:
        print("✓ Test PASSED: Offset appears to be fixed")
    elif result is False:
        print("✗ Test FAILED: Offset still present or other error")
    else:
        print("? Test INCONCLUSIVE: Could not measure offset")

if __name__ == "__main__":
    main()
