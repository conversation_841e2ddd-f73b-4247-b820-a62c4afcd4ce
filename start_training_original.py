#!/usr/bin/env python
"""
SDCNet2D with Mask Support - Original Architecture Training

This script trains the new SDCNet2DMaskOriginal model with the corrected architecture:
- 2 RGB frames input (t-1, t)
- 3 masks (t-1, t, t+1)
- 1 RGB frame output (t+1)
- Compatible with original FlowNet2
"""

import argparse
import os
import sys
import subprocess
import torch
import importlib.util
from datetime import datetime

def import_model():
    """Import the new model."""
    spec = importlib.util.spec_from_file_location(
        "sdc_net2d_mask_original",
        "models/sdc_net2d_mask_original.py"
    )
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module.SDCNet2DMaskOriginal

def get_optimal_parameters():
    """Get optimal training parameters."""
    print("🔧 Configuring optimal parameters...")

    # Base parameters for original architecture
    params = {
        'model': 'SDCNet2DMaskOriginal',
        'dataset': 'MaskFrameLoader',
        'train_file': './dataset/train/',
        'val_file': './dataset/val/',
        'sequence_length': 2,  # FIXED: Original architecture uses 2 frames
        'rgb_max': 255.0,
        'crop_size': [256, 256],
        'epochs': 500,
        'learning_rate': 1e-4,
        'save_freq': 50,
        'val_freq': 10,
        'print_freq': 10,
        'sample_rate': 1,
        'start_index': 0,
        'stride': 64,
    }

    # Adjust based on GPU memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"  GPU memory: {gpu_memory:.1f} GB")
        
        if gpu_memory >= 10:
            params['batch_size'] = 4
            print(f"  High-end GPU: batch_size = {params['batch_size']}")
        elif gpu_memory >= 6:
            params['batch_size'] = 2
            print(f"  Mid-range GPU: batch_size = {params['batch_size']}")
        else:
            params['batch_size'] = 1
            print(f"  Low-end GPU: batch_size = {params['batch_size']}")
    else:
        params['batch_size'] = 1
        print(f"  CPU only: batch_size = {params['batch_size']}")

    return params

def create_training_script():
    """Create a standalone training script."""
    script_content = '''#!/usr/bin/env python
"""
Standalone training script for SDCNet2DMaskOriginal
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
import importlib.util
from torch.utils.data import DataLoader
from datetime import datetime
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def import_model():
    """Import the model."""
    spec = importlib.util.spec_from_file_location(
        "sdc_net2d_mask_original",
        "models/sdc_net2d_mask_original.py"
    )
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module.SDCNet2DMaskOriginal

def create_args():
    """Create arguments."""
    parser = argparse.ArgumentParser()
    
    # Model parameters
    parser.add_argument('--sequence_length', type=int, default=2)
    parser.add_argument('--sample_rate', type=int, default=1)
    parser.add_argument('--crop_size', type=int, nargs=2, default=[256, 256])
    parser.add_argument('--start_index', type=int, default=0)
    parser.add_argument('--stride', type=int, default=64)
    parser.add_argument('--rgb_max', type=float, default=255.0)
    
    # Training parameters
    parser.add_argument('--train_file', default='./dataset/train/')
    parser.add_argument('--val_file', default='./dataset/val/')
    parser.add_argument('--batch_size', type=int, default=2)
    parser.add_argument('--epochs', type=int, default=500)
    parser.add_argument('--learning_rate', type=float, default=1e-4)
    parser.add_argument('--save_freq', type=int, default=50)
    parser.add_argument('--val_freq', type=int, default=10)
    parser.add_argument('--print_freq', type=int, default=10)
    
    # Output
    parser.add_argument('--save_dir', default='./results_original')
    parser.add_argument('--name', default=f'sdcnet_original_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    
    return parser.parse_args()

def create_datasets(args):
    """Create datasets."""
    from datasets.mask_frame_loader import MaskFrameLoader
    
    train_dataset = MaskFrameLoader(args, args.train_file, is_training=True)
    val_dataset = MaskFrameLoader(args, args.val_file, is_training=False)
    
    train_loader = DataLoader(
        train_dataset, batch_size=args.batch_size, shuffle=True,
        num_workers=4, pin_memory=True, drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset, batch_size=args.batch_size, shuffle=False,
        num_workers=4, pin_memory=True, drop_last=True
    )
    
    return train_loader, val_loader

def train_epoch(model, train_loader, optimizer, device, epoch, args):
    """Train one epoch."""
    model.train()
    total_loss = 0
    num_batches = 0
    
    for batch_idx, batch in enumerate(train_loader):
        # Move to device
        for key in batch:
            if key in ['image', 'mask']:
                if isinstance(batch[key], list):
                    batch[key] = [item.to(device) for item in batch[key]]
                else:
                    batch[key] = batch[key].to(device)
        
        optimizer.zero_grad()
        
        # Forward pass
        losses, pred_image, target_image = model(batch)
        loss = losses['tot']
        
        # Backward pass
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        num_batches += 1
        
        if batch_idx % args.print_freq == 0:
            print(f'Epoch {epoch}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.6f}')
    
    return total_loss / num_batches

def validate(model, val_loader, device):
    """Validate model."""
    model.eval()
    total_loss = 0
    num_batches = 0
    
    with torch.no_grad():
        for batch in val_loader:
            # Move to device
            for key in batch:
                if key in ['image', 'mask']:
                    if isinstance(batch[key], list):
                        batch[key] = [item.to(device) for item in batch[key]]
                    else:
                        batch[key] = batch[key].to(device)
            
            # Forward pass
            losses, pred_image, target_image = model(batch)
            loss = losses['tot']
            
            total_loss += loss.item()
            num_batches += 1
    
    return total_loss / num_batches

def main():
    """Main training function."""
    args = create_args()
    
    print("=" * 60)
    print("SDCNet2DMaskOriginal Training")
    print("=" * 60)
    print(f"Train dataset: {args.train_file}")
    print(f"Val dataset: {args.val_file}")
    print(f"Batch size: {args.batch_size}")
    print(f"Epochs: {args.epochs}")
    print(f"Learning rate: {args.learning_rate}")
    print("=" * 60)
    
    # Device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create model
    SDCNet2DMaskOriginal = import_model()
    model = SDCNet2DMaskOriginal(args).to(device)
    
    # Optimizer
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)
    
    # Datasets
    train_loader, val_loader = create_datasets(args)
    print(f"Training samples: {len(train_loader.dataset)}")
    print(f"Validation samples: {len(val_loader.dataset)}")
    
    # Create save directory
    save_dir = os.path.join(args.save_dir, args.name)
    os.makedirs(save_dir, exist_ok=True)
    
    # Training loop
    best_val_loss = float('inf')
    
    for epoch in range(1, args.epochs + 1):
        print(f"\\nEpoch {epoch}/{args.epochs}")
        
        # Train
        train_loss = train_epoch(model, train_loader, optimizer, device, epoch, args)
        print(f"Train Loss: {train_loss:.6f}")
        
        # Validate
        if epoch % args.val_freq == 0:
            val_loss = validate(model, val_loader, device)
            print(f"Val Loss: {val_loss:.6f}")
            
            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                torch.save(model.state_dict(), os.path.join(save_dir, 'best_model.pth'))
                print(f"New best model saved! Val Loss: {val_loss:.6f}")
        
        # Save checkpoint
        if epoch % args.save_freq == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'val_loss': val_loss if epoch % args.val_freq == 0 else None,
            }, os.path.join(save_dir, f'checkpoint_epoch_{epoch}.pth'))
    
    print(f"\\nTraining completed! Best val loss: {best_val_loss:.6f}")
    print(f"Models saved in: {save_dir}")

if __name__ == '__main__':
    main()
'''
    
    with open('train_original.py', 'w') as f:
        f.write(script_content)
    
    print("✅ Training script created: train_original.py")

def main():
    """Main function."""
    print("=" * 60)
    print("SDCNet2DMaskOriginal Training Setup")
    print("=" * 60)
    
    # Check prerequisites
    if not os.path.exists('dataset/train'):
        print("❌ Training dataset not found: dataset/train")
        return False
    
    if not os.path.exists('dataset/val'):
        print("❌ Validation dataset not found: dataset/val")
        return False
    
    # Test model import
    try:
        SDCNet2DMaskOriginal = import_model()
        print("✅ Model import successful")
    except Exception as e:
        print(f"❌ Model import failed: {e}")
        return False
    
    # Get parameters
    params = get_optimal_parameters()
    
    # Create training script
    create_training_script()
    
    print(f"\\n🚀 Ready to start training!")
    print(f"\\nRecommended command:")
    print(f"python train_original.py \\")
    print(f"    --batch_size {params['batch_size']} \\")
    print(f"    --epochs {params['epochs']} \\")
    print(f"    --learning_rate {params['learning_rate']}")
    
    print(f"\\nQuick test (2 epochs):")
    print(f"python train_original.py --epochs 2 --val_freq 1")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
